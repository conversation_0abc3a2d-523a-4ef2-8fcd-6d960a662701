#!/bin/bash

# Docker setup validation script
echo "🔍 Docker Setup Validation"
echo "=========================="

# Check Docker installation
echo "📦 Checking Docker installation..."
if command -v docker >/dev/null 2>&1; then
    echo "✅ Docker is installed: $(docker --version)"
else
    echo "❌ Docker is not installed"
    exit 1
fi

if command -v docker-compose >/dev/null 2>&1; then
    echo "✅ Docker Compose is installed: $(docker-compose --version)"
else
    echo "❌ Docker Compose is not installed"
    exit 1
fi

# Check required files
echo ""
echo "📁 Checking required files..."
required_files=(
    "ai-blog-platform-backend/Dockerfile"
    "ai-blog-platform-frontend/Dockerfile"
    "docker-compose.prod.yml"
    "ai-blog-platform-backend/.dockerignore"
    "ai-blog-platform-frontend/.dockerignore"
)

for file in "${required_files[@]}"; do
    if [ -f "$file" ]; then
        echo "✅ $file exists"
    else
        echo "❌ $file is missing"
        exit 1
    fi
done

# Check environment configuration
echo ""
echo "🔧 Checking environment configuration..."
if [ -f "ai-blog-platform-backend/.env" ]; then
    echo "✅ Backend .env file exists"
    
    # Check for critical environment variables
    critical_vars=("MONGODB_URI" "GOOGLE_CLOUD_PROJECT" "VERTEX_AI_PROJECT")
    for var in "${critical_vars[@]}"; do
        if grep -q "^${var}=" ai-blog-platform-backend/.env; then
            echo "✅ $var is configured"
        else
            echo "⚠️  $var is not configured"
        fi
    done
else
    echo "⚠️  Backend .env file not found"
    echo "   Copy from template: cp ai-blog-platform-backend/.env.production ai-blog-platform-backend/.env"
fi

# Check Google Cloud service account
echo ""
echo "🔑 Checking Google Cloud authentication..."
if [ -f "ai-blog-platform-backend/service_account_key.json" ]; then
    echo "✅ Service account key file exists"
    # Check if it's a valid JSON file
    if python -m json.tool ai-blog-platform-backend/service_account_key.json >/dev/null 2>&1; then
        echo "✅ Service account key is valid JSON"
    else
        echo "❌ Service account key is not valid JSON"
    fi
else
    echo "⚠️  service_account_key.json not found"
    echo "   Vertex AI features will not work without proper authentication"
    echo "   Download from Google Cloud Console and place in ai-blog-platform-backend/"
fi

# Check package.json files
echo ""
echo "📦 Checking package configurations..."
if [ -f "ai-blog-platform-backend/package.json" ]; then
    echo "✅ Backend package.json exists"
    if grep -q "@google-cloud/vertexai" ai-blog-platform-backend/package.json; then
        echo "✅ Vertex AI dependency found"
    else
        echo "⚠️  Vertex AI dependency not found"
    fi
else
    echo "❌ Backend package.json missing"
fi

if [ -f "ai-blog-platform-frontend/package.json" ]; then
    echo "✅ Frontend package.json exists"
else
    echo "❌ Frontend package.json missing"
fi

# Check Next.js configuration
echo ""
echo "⚙️  Checking Next.js configuration..."
if [ -f "ai-blog-platform-frontend/next.config.mjs" ]; then
    if grep -q "output.*standalone" ai-blog-platform-frontend/next.config.mjs; then
        echo "✅ Next.js standalone output configured"
    else
        echo "❌ Next.js standalone output not configured"
        echo "   Add 'output: \"standalone\"' to next.config.mjs"
    fi
else
    echo "❌ next.config.mjs not found"
fi

# Test Docker build (dry run)
echo ""
echo "🧪 Testing Docker configuration..."
if docker-compose -f docker-compose.prod.yml config >/dev/null 2>&1; then
    echo "✅ Docker Compose configuration is valid"
else
    echo "❌ Docker Compose configuration has errors"
    echo "Run: docker-compose -f docker-compose.prod.yml config"
fi

echo ""
echo "📊 Validation Summary"
echo "===================="
echo "✅ Docker setup appears to be correctly configured"
echo ""
echo "🚀 Next steps:"
echo "   1. Ensure all environment variables are set in .env"
echo "   2. Add Google Cloud service account key if using Vertex AI"
echo "   3. Run: ./scripts/docker-deploy.sh"
echo ""
echo "📚 For detailed instructions, see DOCKER_DEPLOYMENT.md"
