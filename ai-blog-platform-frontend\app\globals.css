@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.75rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

/* Enhanced animations and effects */
@keyframes fillScore {
  from {
    stroke-dashoffset: 100%;
  }
  to {
    stroke-dashoffset: var(--stroke-dashoffset);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes bounce {
  0%,
  20%,
  53%,
  80%,
  100% {
    transform: translate3d(0, 0, 0);
  }
  40%,
  43% {
    transform: translate3d(0, -30px, 0);
  }
  70% {
    transform: translate3d(0, -15px, 0);
  }
  90% {
    transform: translate3d(0, -4px, 0);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Enhanced card hover effects */
.keyword-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border: 1px solid #e2e8f0;
}

.keyword-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  border-color: #0066cc;
}

.keyword-card.selected {
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
  border-color: #0066cc;
  box-shadow: 0 0 0 3px rgba(0, 102, 204, 0.1);
}

/* Meta card enhancements */
.meta-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  position: relative;
  overflow: hidden;
}

.meta-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.5s;
}

.meta-card:hover::before {
  left: 100%;
}

.meta-card:hover {
  transform: translateY(-6px);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.15);
}

.meta-card.selected {
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
  border: 2px solid #0066cc;
  box-shadow: 0 0 0 4px rgba(0, 102, 204, 0.1);
}

/* Content block styling */
.content-block {
  transition: all 0.3s ease;
  background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
  border: 1px solid #e5e7eb;
  position: relative;
}

.content-block:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  border-color: #d1d5db;
}

/* Score circle enhancements */
.score-circle {
  animation: fillScore 1.5s ease-out;
  filter: drop-shadow(0 4px 6px rgba(0, 0, 0, 0.1));
}

/* Stepper enhancements */
.stepper-step {
  transition: all 0.3s ease;
}

.stepper-step.completed {
  animation: bounce 0.6s ease-in-out;
}

.stepper-progress {
  background: linear-gradient(90deg, #0066cc 0%, #00aa66 100%);
  background-size: 200% 200%;
  animation: gradient 3s ease infinite;
}

/* Button enhancements */
.btn-primary {
  background: linear-gradient(135deg, #0066cc 0%, #0052a3 100%);
  box-shadow: 0 4px 14px 0 rgba(0, 102, 204, 0.3);
  transition: all 0.3s ease;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #0052a3 0%, #003d7a 100%);
  box-shadow: 0 6px 20px 0 rgba(0, 102, 204, 0.4);
  transform: translateY(-2px);
}

.btn-secondary {
  background: linear-gradient(135deg, #00aa66 0%, #008a52 100%);
  box-shadow: 0 4px 14px 0 rgba(0, 170, 102, 0.3);
  transition: all 0.3s ease;
}

.btn-secondary:hover {
  background: linear-gradient(135deg, #008a52 0%, #006b3f 100%);
  box-shadow: 0 6px 20px 0 rgba(0, 170, 102, 0.4);
  transform: translateY(-2px);
}

/* Loading shimmer effect */
.shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

/* Prose enhancements */
.prose {
  color: #374151;
  max-width: none;
  line-height: 1.75;
}

.prose h1 {
  color: #111827;
  font-weight: 800;
  font-size: 2.25rem;
  margin-top: 0;
  margin-bottom: 2rem;
  line-height: 1.2;
  background: linear-gradient(135deg, #0066cc 0%, #00aa66 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.prose h2 {
  color: #111827;
  font-weight: 700;
  font-size: 1.875rem;
  margin-top: 2.5rem;
  margin-bottom: 1.25rem;
  line-height: 1.3;
  position: relative;
  padding-left: 1rem;
}

.prose h2::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: linear-gradient(135deg, #0066cc 0%, #00aa66 100%);
  border-radius: 2px;
}

.prose p {
  margin-top: 1.25rem;
  margin-bottom: 1.25rem;
  line-height: 1.8;
}

.prose strong {
  color: #111827;
  font-weight: 600;
}

.prose a {
  color: #0066cc;
  text-decoration: none;
  font-weight: 500;
  border-bottom: 2px solid transparent;
  transition: all 0.3s ease;
}

.prose a:hover {
  color: #0052a3;
  border-bottom-color: #0066cc;
}

.prose ul {
  margin-top: 1.25rem;
  margin-bottom: 1.25rem;
  padding-left: 1.75rem;
}

.prose li {
  margin-top: 0.75rem;
  margin-bottom: 0.75rem;
  position: relative;
}

.prose li::marker {
  color: #0066cc;
}

/* Badge enhancements */
.badge-ai {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
  color: white;
  box-shadow: 0 2px 4px rgba(139, 92, 246, 0.3);
}

.badge-manual {
  background: linear-gradient(135deg, #0066cc 0%, #0052a3 100%);
  color: white;
  box-shadow: 0 2px 4px rgba(0, 102, 204, 0.3);
}

.badge-success {
  background: linear-gradient(135deg, #00aa66 0%, #008a52 100%);
  color: white;
  box-shadow: 0 2px 4px rgba(0, 170, 102, 0.3);
}

/* Animation classes */
.fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.slide-in-right {
  animation: slideInRight 0.6s ease-out;
}

.pulse-animation {
  animation: pulse 2s infinite;
}

/* Responsive enhancements */
@media (max-width: 640px) {
  .keyword-card:hover {
    transform: translateY(-4px) scale(1.01);
  }

  .meta-card:hover {
    transform: translateY(-3px);
  }
}

/* Dark mode enhancements */
.dark .keyword-card {
  background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
  border-color: #374151;
}

.dark .keyword-card:hover {
  border-color: #60a5fa;
}

.dark .meta-card {
  background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
}

.dark .content-block {
  background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
  border-color: #374151;
}

/* Tooltip enhancements */
.tooltip {
  background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
  border: 1px solid #374151;
  box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.3);
}

/* Focus states */
.focus-ring:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(0, 102, 204, 0.1);
  border-color: #0066cc;
}

/* Success states */
.success-glow {
  box-shadow: 0 0 20px rgba(0, 170, 102, 0.3);
  border-color: #00aa66;
}

/* Error states */
.error-glow {
  box-shadow: 0 0 20px rgba(239, 68, 68, 0.3);
  border-color: #ef4444;
}
