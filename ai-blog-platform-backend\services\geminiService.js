// services/geminiService.js
const { VertexAI, HarmCategory, HarmBlockThreshold } = require('@google-cloud/vertexai');
const path = require('path');
require('dotenv').config();

class GeminiService {
  constructor() {
    // Initialize Vertex AI client - support multiple env var formats
    this.project = process.env.GOOGLE_CLOUD_PROJECT ||
                   process.env.VERTEX_AI_PROJECT ||
                   process.env.VERTEX_AI_PROJECT_ID;
    this.location = process.env.GOOGLE_CLOUD_LOCATION ||
                    process.env.VERTEX_AI_LOCATION ||
                    process.env.VERTEX_AI_REGION ||
                    'us-central1';

    if (!this.project) {
      console.warn('⚠️ GOOGLE_CLOUD_PROJECT not set. Using fallback content generation.');
      this.vertexAI = null;
      this.generativeModel = null;
      return;
    }

    try {
      // Set up authentication - check for service account key file
      const serviceAccountPath = process.env.GOOGLE_APPLICATION_CREDENTIALS ||
                                path.join(__dirname, '..', 'service_account_key.json');

      // Set the environment variable for Google Cloud authentication
      if (!process.env.GOOGLE_APPLICATION_CREDENTIALS) {
        process.env.GOOGLE_APPLICATION_CREDENTIALS = serviceAccountPath;
        console.log(`🔑 Using service account key: ${serviceAccountPath}`);
      }

      // Initialize Vertex AI with explicit configuration
      this.vertexAI = new VertexAI({
        project: this.project,
        location: this.location,
      });

      // Use model from env or default to gemini-2.0-flash-exp (latest available)
      // Note: gemini-2.5-pro is not yet available, using gemini-2.0-flash-exp for best performance
      this.model = process.env.VERTEX_AI_MODEL_NAME || 'gemini-2.0-flash-exp';

      // Configure safety settings
      this.safetySettings = [
        { category: HarmCategory.HARM_CATEGORY_HARASSMENT, threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE },
        { category: HarmCategory.HARM_CATEGORY_HATE_SPEECH, threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE },
        { category: HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT, threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE },
        { category: HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT, threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE }
      ];

      // Generation configuration optimized for Gemini 2.0 Flash
      this.generationConfig = {
        maxOutputTokens: 8192,
        temperature: 0.7,
        topP: 0.95,
        topK: 40,
        candidateCount: 1,
        stopSequences: []
      };

      // Get the model instance
      this.generativeModel = this.vertexAI.getGenerativeModel({
        model: this.model,
        generationConfig: this.generationConfig,
        safetySettings: this.safetySettings
      });

      // Test the connection (commented out for production)
      // console.log('🔍 Testing Vertex AI connection...');
      // this.testConnection();

      console.log(`✅ Vertex AI initialized successfully!`);
      console.log(`   Project: ${this.project}`);
      console.log(`   Location: ${this.location}`);
      console.log(`   Model: ${this.model}`);
      console.log(`   Authentication: Service Account`);
      console.log(`   SDK: @google-cloud/vertexai (latest)`);
    } catch (error) {
      console.error('❌ Failed to initialize Vertex AI:', error.message);
      console.error('   Error details:', error);
      this.vertexAI = null;
      this.generativeModel = null;
    }
  }

  async testConnection() {
    if (!this.vertexAI || !this.generativeModel) {
      return false;
    }

    try {
      const testRequest = {
        contents: [{
          role: 'user',
          parts: [{ text: 'Hello, this is a test. Please respond with "Connection successful".' }]
        }]
      };

      const result = await this.generativeModel.generateContent(testRequest);
      const response = result.response;

      if (response && response.candidates && response.candidates.length > 0) {
        console.log('✅ Vertex AI connection test successful');
        return true;
      } else {
        console.log('⚠️ Vertex AI connection test failed - no response');
        return false;
      }
    } catch (error) {
      console.error('❌ Vertex AI connection test failed:', error.message);
      if (error.message.includes('permission')) {
        console.error('🔑 Check service account permissions for Vertex AI');
      }
      return false;
    }
  }

  async generateContent(prompt, companyContext = {}) {
    const validatedContext = this.validateCompanyContext(companyContext);

    if (!this.vertexAI || !this.generativeModel) {
      console.warn('⚠️ Vertex AI not configured, using high-quality fallback content');
      return this.generateHighQualityFallback(prompt, validatedContext);
    }

    try {
      console.log('🤖 Generating content with Vertex AI...');
      const contextualPrompt = this.buildContextualPrompt(prompt, validatedContext);

      // Vertex AI SDK request format
      const request = {
        contents: [{
          role: 'user',
          parts: [{ text: contextualPrompt }]
        }]
      };

      console.log(`📝 Prompt length: ${contextualPrompt.length} characters`);

      const result = await this.generativeModel.generateContent(request);
      const response = result.response;

      if (!response) {
        throw new Error('No response received from Vertex AI');
      }

      if (!response.candidates || !response.candidates.length) {
        console.warn('⚠️ No candidates in Vertex AI response, using fallback');
        return this.generateHighQualityFallback(prompt, validatedContext);
      }

      const candidate = response.candidates[0];

      // Check for safety filtering
      if (candidate.finishReason === 'SAFETY') {
        console.warn('⚠️ Content filtered by Vertex AI safety settings, using fallback');
        return this.generateHighQualityFallback(prompt, validatedContext);
      }

      if (!candidate.content || !candidate.content.parts || !candidate.content.parts.length) {
        console.warn('⚠️ Invalid response format from Vertex AI, using fallback');
        return this.generateHighQualityFallback(prompt, validatedContext);
      }

      const generatedText = candidate.content.parts[0].text;

      if (!generatedText || generatedText.trim().length === 0) {
        console.warn('⚠️ Empty content from Vertex AI, using fallback');
        return this.generateHighQualityFallback(prompt, validatedContext);
      }

      let finalContent = generatedText;
      const actualWordCount = generatedText.split(' ').length;

      // Extract target word count from prompt if available
      const wordCountMatch = prompt.match(/(?:exactly|MAXIMUM)\s+(\d+)\s+words/i);
      if (wordCountMatch) {
        const targetWordCount = parseInt(wordCountMatch[1]);

        if (actualWordCount > targetWordCount) {
          console.log(`✂️ VERTEX AI FORCE TRIM: ${actualWordCount} words → EXACTLY ${targetWordCount} words`);

          // AGGRESSIVELY trim to exact word count - NO TOLERANCE
          const words = generatedText.split(' ').filter(w => w.trim().length > 0);
          const trimmedWords = words.slice(0, targetWordCount);
          finalContent = trimmedWords.join(' ');

          // Ensure proper sentence ending without adding extra words
          if (!finalContent.endsWith('.') && !finalContent.endsWith('!') && !finalContent.endsWith('?')) {
            // Remove last word if needed to add period without exceeding count
            const wordsArray = finalContent.split(' ');
            if (wordsArray.length >= targetWordCount) {
              wordsArray.pop(); // Remove last word
              finalContent = wordsArray.join(' ') + '.';
            } else {
              finalContent = finalContent + '.';
            }
          }

          const finalCount = finalContent.split(' ').filter(w => w.trim().length > 0).length;
          console.log(`✅ FORCE TRIMMED: ${actualWordCount} → ${finalCount} words (Target: ${targetWordCount})`);
        }
      }

      const finalWordCount = finalContent.split(' ').length;
      console.log(`✅ Vertex AI final content: ${finalWordCount} words`);

      return {
        content: finalContent,
        keywords: this.extractKeywords(finalContent),
        wordCount: finalWordCount,
        source: 'vertex-ai'
      };
    } catch (error) {
      console.error('❌ Vertex AI content generation failed:', error.message);

      // Log specific error types for debugging
      if (error.code === 'PERMISSION_DENIED') {
        console.error('🔑 Authentication issue - check service account permissions');
      } else if (error.code === 'QUOTA_EXCEEDED') {
        console.error('📊 Quota exceeded - check Vertex AI usage limits');
      } else if (error.code === 'INVALID_ARGUMENT') {
        console.error('📝 Invalid request format - check prompt structure');
      }

      console.log('🔄 Falling back to high-quality template content');
      return this.generateHighQualityFallback(prompt, validatedContext);
    }
  }

  validateCompanyContext(companyContext = {}) {
    const validated = { ...companyContext };

    if (!validated.name) {
      console.warn('⚠️ Company name missing in context, defaulting to WattMonk');
      validated.name = 'WattMonk';
    }

    if (!validated.servicesOffered) {
      console.warn('⚠️ Company services missing in context, using default');
      validated.servicesOffered = 'Solar Design, Engineering, Permitting, Installation Support';
    }

    if (!validated.serviceOverview) {
      console.warn('⚠️ Company service overview missing in context, using default');
      validated.serviceOverview = 'Professional solar design, engineering, permitting, and installation support services';
    }

    if (!validated.aboutTheCompany) {
      console.warn('⚠️ Company description missing in context, using default');
      validated.aboutTheCompany = 'WattMonk is a technology-driven solar services company providing end-to-end solar solutions.';
    }

    console.log('✅ Company context validated for:', validated.name);
    return validated;
  }

  generateHighQualityFallback(prompt, companyContext = {}) {
    console.log('🎯 Generating high-quality fallback content for prompt:', prompt.substring(0, 100) + '...');
    
    const keyword = companyContext.keyword || this.extractKeywordFromPrompt(prompt);
    const companyName = companyContext.name || 'WattMonk';

    // Detect content type and generate accordingly
    if (prompt.toLowerCase().includes('meta title') || prompt.toLowerCase().includes('metatitle')) {
      return {
        content: keyword.charAt(0).toUpperCase() + keyword.slice(1) + ' Solutions | ' + companyName + ' Expert Guide',
        keywords: [keyword, 'solutions', 'guide'],
        wordCount: 8
      };
    }

    if (prompt.toLowerCase().includes('meta description') || prompt.toLowerCase().includes('metadescription')) {
      return {
        content: 'Discover comprehensive ' + keyword + ' solutions with ' + companyName + '. Expert insights, practical tips, and proven strategies for solar professionals and homeowners.',
        keywords: [keyword, 'solutions', 'expert', 'solar'],
        wordCount: 22
      };
    }

    if (prompt.toLowerCase().includes('h1') || prompt.toLowerCase().includes('title') || prompt.toLowerCase().includes('heading')) {
      const h1Options = [
        'Complete ' + keyword.charAt(0).toUpperCase() + keyword.slice(1) + ' Guide for Solar Professionals',
        keyword.charAt(0).toUpperCase() + keyword.slice(1) + ': Expert Solutions and Best Practices',
        'Professional ' + keyword.charAt(0).toUpperCase() + keyword.slice(1) + ' Implementation Guide'
      ];
      const selectedH1 = h1Options[Math.floor(Math.random() * h1Options.length)];

      return {
        content: selectedH1,
        keywords: [keyword, 'guide', 'professional', 'solar'],
        wordCount: selectedH1.split(' ').length
      };
    }

    // For longer content
    const contentTemplates = this.getContentTemplates(keyword, companyName, companyContext);
    const selectedTemplate = contentTemplates[Math.floor(Math.random() * contentTemplates.length)];

    return {
      content: selectedTemplate,
      keywords: this.extractKeywords(selectedTemplate),
      wordCount: selectedTemplate.split(' ').length
    };
  }

  extractKeywordFromPrompt(prompt) {
    const patterns = [
      /for (?:the )?(?:keyword |focus keyword )?["']([^"']+)["']/i,
      /about ["']([^"']+)["']/i,
      /regarding ["']([^"']+)["']/i,
      /on ["']([^"']+)["']/i,
      /titled? ["']([^"']+)["']/i
    ];

    for (const pattern of patterns) {
      const match = prompt.match(pattern);
      if (match) {
        return match[1].toLowerCase();
      }
    }

    return 'solar energy solutions';
  }

  getContentTemplates(keyword, companyName, companyContext = {}) {
    const services = companyContext.servicesOffered || 'solar design and engineering services';
    const cleanKeyword = keyword.toLowerCase().replace(/[^a-z0-9\s]/g, '').trim();
    const titleKeyword = cleanKeyword.charAt(0).toUpperCase() + cleanKeyword.slice(1);

    // Generate contextual content based on keyword type
    const isComparison = cleanKeyword.includes('comparison') || cleanKeyword.includes('vs') || cleanKeyword.includes('compare');
    const isSoftware = cleanKeyword.includes('software') || cleanKeyword.includes('tool') || cleanKeyword.includes('platform');
    const isGuide = cleanKeyword.includes('guide') || cleanKeyword.includes('how to') || cleanKeyword.includes('step');

    if (isSoftware) {
      return [this.generateSoftwareContent(titleKeyword, companyName, services)];
    } else if (isComparison) {
      return [this.generateComparisonContent(titleKeyword, companyName, services)];
    } else if (isGuide) {
      return [this.generateGuideContent(titleKeyword, companyName, services)];
    } else {
      return [this.generateGeneralContent(titleKeyword, companyName, services)];
    }
  }

  generateSoftwareContent(keyword, companyName, services) {
    return '<h2>Understanding ' + keyword + ' Solutions</h2>' +
      '<p>In today\'s competitive solar market, choosing the right software solution is crucial for project success. <b>' + keyword + '</b> represents a significant advancement in solar technology, enabling professionals to streamline their workflows and deliver superior results.</p>' +
      '<p>At <b>' + companyName + '</b>, we leverage cutting-edge software tools to enhance our ' + services + '. Our experience with various platforms allows us to recommend the most suitable solutions for each project\'s unique requirements.</p>' +
      '<h2>Key Features and Capabilities</h2>' +
      '<ul>' +
      '<li><b>Advanced modeling capabilities</b> for accurate system design</li>' +
      '<li><b>Real-time collaboration tools</b> for team coordination</li>' +
      '<li><b>Automated reporting features</b> for streamlined documentation</li>' +
      '<li><b>Integration capabilities</b> with existing workflows</li>' +
      '</ul>' +
      '<p>Our team stays current with the latest software developments to ensure our clients benefit from the most advanced tools available in the solar industry.</p>';
  }

  generateComparisonContent(keyword, companyName, services) {
    return '<h2>' + keyword + ': Making the Right Choice</h2>' +
      '<p>When evaluating different options in the solar industry, understanding the nuances of <b>' + keyword + '</b> is essential for making informed decisions. <b>' + companyName + '</b> brings extensive experience in ' + services + ' to help clients navigate these important choices.</p>' +
      '<p>Our comprehensive analysis approach considers multiple factors including performance, cost-effectiveness, long-term reliability, and compatibility with existing systems.</p>' +
      '<h2>Comparison Framework</h2>' +
      '<table>' +
      '<thead><tr><th>Criteria</th><th>Importance</th><th>Considerations</th></tr></thead>' +
      '<tbody>' +
      '<tr><td><b>Performance</b></td><td>High</td><td>Efficiency ratings and output metrics</td></tr>' +
      '<tr><td><b>Cost</b></td><td>High</td><td>Initial investment and long-term value</td></tr>' +
      '<tr><td><b>Reliability</b></td><td>Critical</td><td>Track record and warranty coverage</td></tr>' +
      '</tbody></table>' +
      '<p>Our expert team provides detailed analysis and recommendations based on your specific project requirements and objectives.</p>';
  }

  generateGuideContent(keyword, companyName, services) {
    return '<h2>' + keyword + ': Step-by-Step Approach</h2>' +
      '<p>Successfully implementing <b>' + keyword + '</b> requires careful planning and expert execution. <b>' + companyName + '</b> has developed proven methodologies through our extensive work in ' + services + '.</p>' +
      '<p>Our systematic approach ensures that every project meets the highest standards of quality and performance while adhering to all relevant regulations and best practices.</p>' +
      '<h2>Implementation Process</h2>' +
      '<ol>' +
      '<li><b>Initial Assessment</b> - Comprehensive site evaluation and requirements analysis</li>' +
      '<li><b>Design Development</b> - Custom solution design based on specific needs</li>' +
      '<li><b>Planning and Preparation</b> - Detailed project planning and resource allocation</li>' +
      '<li><b>Implementation</b> - Professional execution with quality control measures</li>' +
      '<li><b>Testing and Validation</b> - Thorough testing to ensure optimal performance</li>' +
      '</ol>' +
      '<p>Our experienced team guides clients through each phase, ensuring successful project completion and long-term satisfaction.</p>';
  }

  generateGeneralContent(keyword, companyName, services) {
    return '<h2>Understanding ' + keyword + '</h2>' +
      '<p><b>' + keyword + '</b> plays a crucial role in modern solar energy systems, offering significant benefits for both residential and commercial applications. <b>' + companyName + '</b> specializes in ' + services + ', providing clients with expert guidance and professional implementation.</p>' +
      '<p>Our comprehensive approach combines industry best practices with innovative solutions to deliver exceptional results for every project. We stay current with the latest developments to ensure our clients benefit from cutting-edge technology and proven methodologies.</p>' +
      '<h2>Key Benefits and Advantages</h2>' +
      '<ul>' +
      '<li><b>Enhanced system performance</b> through optimized design and implementation</li>' +
      '<li><b>Cost-effective solutions</b> tailored to specific requirements</li>' +
      '<li><b>Professional expertise</b> backed by years of industry experience</li>' +
      '<li><b>Ongoing support</b> for long-term success and satisfaction</li>' +
      '</ul>' +
      '<p>Our team provides comprehensive consultation, custom design solutions, and professional implementation services to ensure optimal results for every project.</p>';
  }

  buildContextualPrompt(prompt, companyContext) {
    const cleanContext = this.cleanContext(companyContext);
    
    let contextualPrompt = 'Company Information:\n';
    contextualPrompt += '- Name: ' + (cleanContext.name || 'WattMonk') + '\n';
    contextualPrompt += '- Services: ' + (cleanContext.servicesOffered || 'Solar Design, Engineering, Permitting, Installation Support') + '\n';
    contextualPrompt += '- About: ' + (cleanContext.aboutTheCompany || 'Professional solar services company') + '\n\n';
    contextualPrompt += 'Content Requirements:\n';
    contextualPrompt += '- Write as ' + (cleanContext.name || 'WattMonk') + ' expert\n';
    contextualPrompt += '- Use HTML formatting (<p>, <h2>, <h3>, <ul>, <li>, <b>, <i>, <table>)\n';
    contextualPrompt += '- Include company mentions naturally\n';
    contextualPrompt += '- Professional, informative tone\n';
    contextualPrompt += '- Focus on providing value to solar professionals\n\n';
    contextualPrompt += 'Original Request: ' + prompt;

    return contextualPrompt;
  }

  // Fixed structured blog content generation
  async generateStructuredBlogContent(draftData, trendData = []) {
    const selectedKeyword = draftData.selectedKeyword || '';
    const selectedH1 = draftData.selectedH1 || '';
    const selectedMetaTitle = draftData.selectedMetaTitle || '';
    const selectedMetaDescription = draftData.selectedMetaDescription || '';
    const companyName = draftData.companyName || 'WattMonk';
    const companyContext = draftData.companyContext || {};
    const targetWordCount = draftData.targetWordCount || 2000;

    console.log('🎯 Generating structured blog content for:', selectedKeyword);
    console.log('🏢 Company:', companyName, 'Target:', targetWordCount, 'words');

    try {
      // Generate link data if linkService is available
      let linkData = { inboundLinks: [], outboundLinks: [] };
      try {
        const linkService = require('./linkService');
        linkData = await linkService.generateInboundOutboundLinks(selectedKeyword, companyName, trendData);
        console.log('🔗 Generated', linkData.inboundLinks.length, 'inbound and', linkData.outboundLinks.length, 'outbound links');
      } catch (error) {
        console.warn('⚠️ LinkService not available, proceeding without links');
      }

      // Create a simplified, more reliable prompt
      const blogPrompt = 'Write a comprehensive blog article about "' + selectedKeyword + '" for ' + companyName + '.\n\n' +
        'ARTICLE DETAILS:\n' +
        '- Title: ' + selectedH1 + '\n' +
        '- Target Word Count: ' + targetWordCount + ' words\n' +
        '- Company: ' + companyName + '\n' +
        '- Services: ' + (companyContext.servicesOffered || 'Solar Design, Engineering, Permitting, Installation Support') + '\n\n' +
        'CONTENT STRUCTURE:\n' +
        '1. Introduction (10% of word count)\n' +
        '2. 4-5 main sections with H2 headings (70% of word count)\n' +
        '3. Conclusion (20% of word count)\n\n' +
        'FORMAT REQUIREMENTS:\n' +
        '- Use proper HTML tags: <h2>, <p>, <ul>, <li>, <b>, <i>, <table>\n' +
        '- Include ' + companyName + ' mentions naturally throughout\n' +
        '- Write in professional, informative tone\n' +
        '- Include practical examples and actionable advice\n' +
        '- Focus on solar industry expertise\n\n' +
        'Write the complete article content only - no JSON formatting needed.';

      const result = await this.generateContent(blogPrompt, {
        name: companyName,
        keyword: selectedKeyword,
        ...companyContext
      });

      let generatedContent;
      if (result.success === false) {
        console.warn('⚠️ Using fallback content due to Gemini failure');
        generatedContent = result.content;
      } else {
        generatedContent = result.content;
      }
      
      // Clean and enhance the content
      generatedContent = this.cleanGeneratedContent(generatedContent);
      generatedContent = this.ensureProperFormatting(generatedContent);
      generatedContent = this.enhanceContentWithCompanyInfo(generatedContent, companyContext, selectedKeyword);

      // Properly integrate links into content (DISABLED for now to prevent nesting)
      // generatedContent = this.integrateLinksIntoContent(generatedContent, linkData, selectedKeyword);

      // FINAL CLEANUP: Remove all broken link patterns
      generatedContent = this.cleanupBrokenLinks(generatedContent);

      // Add simple References section without complex link integration
      if (linkData.outboundLinks && linkData.outboundLinks.length > 0) {
        let referencesSection = '\n\n<h2 style="color: #FBD46F; font-family: Roboto; font-weight: 600;">References and Further Reading</h2>\n\n<p>These authoritative sources provide additional insights about ' + selectedKeyword + ':</p>\n\n<ul>\n';

        linkData.outboundLinks.slice(0, 5).forEach(link => {
          referencesSection += `<li><a href="${link.url}" target="_blank">${link.text}</a> - ${link.context || 'Authoritative resource on ' + selectedKeyword}</li>\n`;
        });

        referencesSection += '</ul>\n\n<p><em>Note: These links open in new windows and are provided for additional research and verification.</em></p>';

        generatedContent += referencesSection;
      }
      
      // Add H2 styling and ensure proper content structure
      generatedContent = this.improveContentStructure(generatedContent, selectedKeyword);

      // Check and adjust word count if necessary
      const actualWordCount = this.countWords(generatedContent);
      if (actualWordCount > targetWordCount * 1.2) {
        console.log('⚠️ Content too long (' + actualWordCount + ' words), truncating to ' + targetWordCount);
        generatedContent = this.truncateContent(generatedContent, targetWordCount);
      }

      const finalWordCount = this.countWords(generatedContent);
      console.log('📊 Final content:', finalWordCount, 'words (target:', targetWordCount + ')');

      return {
        success: true,
        content: {
          title: selectedH1,
          metaTitle: selectedMetaTitle,
          metaDescription: selectedMetaDescription,
          content: generatedContent,
          internalLinks: linkData.inboundLinks || [],
          externalLinks: linkData.outboundLinks || []
        },
        wordCount: finalWordCount,
        seoScore: this.calculateSEOScore(generatedContent, selectedKeyword, selectedMetaTitle, selectedMetaDescription),
        message: 'Blog content generated successfully (' + finalWordCount + ' words)'
      };

    } catch (error) {
      console.error('❌ Structured content generation failed:', error.message);
      return this.generateFallbackBlogContent(selectedKeyword, selectedH1, companyContext, targetWordCount);
    }
  }

  // Fixed content cleaning
  cleanGeneratedContent(content) {
    if (!content || typeof content !== 'string') {
      return '';
    }

    // Remove HTML document structure if present (keep only body content)
    content = content.replace(/<!DOCTYPE[^>]*>/gi, '');
    content = content.replace(/<html[^>]*>/gi, '');
    content = content.replace(/<\/html>/gi, '');
    content = content.replace(/<head[^>]*>[\s\S]*?<\/head>/gi, '');
    content = content.replace(/<body[^>]*>/gi, '');
    content = content.replace(/<\/body>/gi, '');
    content = content.replace(/<title[^>]*>[\s\S]*?<\/title>/gi, '');

    // Remove code blocks and JSON formatting
    content = content.replace(/```(?:html|json)?([\s\S]*?)```/g, '$1');
    content = content.replace(/`[^`]*`/g, ''); // Remove inline code

    // Remove database object references (but keep regular JSON-like content)
    content = content.replace(/\{\s*name:\s*['"][^'"]*['"],\s*description:\s*['"][^'"]*['"],\s*_id:\s*[^}]*\}/g, '');
    content = content.replace(/ObjectId\([^)]*\)/g, '');

    // Remove HTML comments
    content = content.replace(/<!--[\s\S]*?-->/g, '');

    // Clean up extra whitespace but preserve line breaks
    content = content.replace(/[ \t]+/g, ' ');
    content = content.replace(/\n\s*\n/g, '\n');
    content = content.trim();

    return content;
  }

  // Fixed HTML formatting
  ensureProperFormatting(content) {
    if (!content) {
      return '';
    }

    // Ensure paragraphs are wrapped
    const lines = content.split('\n').filter(function(line) {
      return line.trim();
    });
    let formatted = '';

    for (let i = 0; i < lines.length; i++) {
      let line = lines[i].trim();
      if (!line) {
        continue;
      }

      // Check if it's already a heading or list item
      if (line.startsWith('<h') || line.startsWith('<ul') || line.startsWith('<ol') || line.startsWith('<li') || line.startsWith('<table')) {
        formatted += line + '\n';
      }
      // Wrap regular text in paragraph tags
      else if (!line.startsWith('<')) {
        formatted += '<p>' + line + '</p>\n';
      }
      // Keep existing HTML tags
      else {
        formatted += line + '\n';
      }
    }

    return formatted.trim();
  }

  // Fixed content cleaning
  cleanGeneratedContent(content) {
    if (!content || typeof content !== 'string') {
      return '';
    }

    // Remove HTML document structure if present (keep only body content)
    content = content.replace(/<!DOCTYPE[^>]*>/gi, '');
    content = content.replace(/<html[^>]*>/gi, '');
    content = content.replace(/<\/html>/gi, '');
    content = content.replace(/<head[^>]*>[\s\S]*?<\/head>/gi, '');
    content = content.replace(/<body[^>]*>/gi, '');
    content = content.replace(/<\/body>/gi, '');
    content = content.replace(/<title[^>]*>[\s\S]*?<\/title>/gi, '');

    // Remove code blocks and JSON formatting
    content = content.replace(/```(?:html|json)?([\s\S]*?)```/g, '$1');
    content = content.replace(/`[^`]*`/g, ''); // Remove inline code

    // Remove database object references (but keep regular JSON-like content)
    content = content.replace(/\{\s*name:\s*['"][^'"]*['"],\s*description:\s*['"][^'"]*['"],\s*_id:\s*[^}]*\}/g, '');
    content = content.replace(/ObjectId\([^)]*\)/g, '');

    // Remove HTML comments
    content = content.replace(/<!--[\s\S]*?-->/g, '');

    // Clean up extra whitespace but preserve line breaks
    content = content.replace(/[ \t]+/g, ' ');
    content = content.replace(/\n\s*\n/g, '\n');
    content = content.trim();

    return content;
  }

  // Fixed HTML formatting
  ensureProperFormatting(content) {
    if (!content) {
      return '';
    }

    // Ensure paragraphs are wrapped
    const lines = content.split('\n').filter(function(line) {
      return line.trim();
    });
    let formatted = '';

    for (let i = 0; i < lines.length; i++) {
      let line = lines[i].trim();
      if (!line) {
        continue;
      }

      // Check if it's already a heading or list item
      if (line.startsWith('<h') || line.startsWith('<ul') || line.startsWith('<ol') || line.startsWith('<li') || line.startsWith('<table')) {
        formatted += line + '\n';
      }
      // Wrap regular text in paragraph tags
      else if (!line.startsWith('<')) {
        formatted += '<p>' + line + '</p>\n';
      }
      // Keep existing HTML tags
      else {
        formatted += line + '\n';
      }
    }

    return formatted;
  }

  // Improved content enhancement with company info
  enhanceContentWithCompanyInfo(content, companyContext, keyword) {
    if (!content || !companyContext.name) {
      return content;
    }

    const companyName = companyContext.name;
    const services = companyContext.servicesOffered || 'solar services';

    // Count existing company mentions
    const regex = new RegExp(companyName, 'gi');
    const matches = content.match(regex);
    const companyMentions = matches ? matches.length : 0;
    console.log('🏢 Found', companyMentions, 'mentions of', companyName);

    // If no company mentions, add some naturally
    if (companyMentions === 0) {
      // Add mention in first paragraph
      content = content.replace(
        /<p>([^<]{50,}?)<\/p>/,
        '<p>$1 At <b>' + companyName + '</b>, our expertise in ' + services + ' helps optimize ' + keyword + ' implementations for maximum efficiency.</p>'
      );

      // Add mention near the end
      const lastPIndex = content.lastIndexOf('<p>');
      if (lastPIndex > -1) {
        const beforeLast = content.substring(0, lastPIndex);
        const lastP = content.substring(lastPIndex);
        content = beforeLast + '<p><b>' + companyName + '</b> provides comprehensive ' + keyword + ' solutions through our ' + services + '. Contact our team for professional consultation and implementation support.</p>\n' + lastP;
      }
    }

    return content;
  }

  // Integrate links properly into content with correct HTML formatting
  integrateLinksIntoContent(content, linkData, keyword) {
    if (!linkData || (!linkData.inboundLinks && !linkData.outboundLinks)) {
      return content;
    }

    let enhancedContent = content;

    // SKIP internal links for now to avoid nesting issues
    // We'll add them in References section only

    // Add external authority links in References section
    if (linkData.outboundLinks && linkData.outboundLinks.length > 0) {
      let referencesSection = '\n\n<h2 style="color: #FBD46F; font-family: Roboto; font-weight: 600;">References and Further Reading</h2>\n\n<p>These authoritative sources provide additional insights about ' + keyword + ':</p>\n\n<ul>\n';

      linkData.outboundLinks.slice(0, 5).forEach(link => {
        // SIMPLE blue links that open in new tabs
        referencesSection += `<li><a href="${link.url}" target="_blank">${link.text}</a> - ${link.context || 'Authoritative resource on ' + keyword}</li>\n`;
      });

      referencesSection += '</ul>\n\n<p><em>Note: These links open in new windows and are provided for additional research and verification.</em></p>';

      enhancedContent += referencesSection;
    }

    return enhancedContent;
  }

  // Clean up all broken link patterns aggressively
  cleanupBrokenLinks(content) {
    let cleanContent = content;

    // Remove nested <a> tags completely
    cleanContent = cleanContent.replace(
      /<a href="<a href="[^"]*"[^>]*>[^<]*<\/a>[^"]*"[^>]*>[^<]*<\/a>/g,
      ''
    );

    // Remove broken link patterns with target="_blank"
    cleanContent = cleanContent.replace(
      /https:\/\/[^\s"]+"\s*target="_blank"[^>]*>/g,
      ''
    );

    // Remove orphaned link attributes
    cleanContent = cleanContent.replace(
      /rel="noopener noreferrer"\s*class="[^"]*">[^<]*<svg[^>]*>.*?<\/svg>/g,
      ''
    );

    // Remove any remaining broken link fragments
    cleanContent = cleanContent.replace(
      /"\s*target="_blank"\s*rel="noopener noreferrer"[^>]*>/g,
      ''
    );

    // Clean up extra spaces and line breaks
    cleanContent = cleanContent.replace(/\s+/g, ' ');
    cleanContent = cleanContent.replace(/>\s+</g, '><');

    return cleanContent;
  }

  // Improve content structure with proper headings and formatting
  improveContentStructure(content, keyword) {
    let improvedContent = content;

    // Add H2 styling
    improvedContent = improvedContent.replace(
      /<h2[^>]*>/g,
      "<h2 style='color: #FBD46F;'>"
    );

    // Ensure proper spacing between sections
    improvedContent = improvedContent.replace(
      /<\/h2>\s*<p>/g,
      '</h2>\n\n<p>'
    );

    // Fix any malformed HTML links (DISABLED for now to prevent nesting)
    // improvedContent = this.fixMalformedLinks(improvedContent);

    // Ensure proper paragraph spacing
    improvedContent = improvedContent.replace(
      /<\/p>\s*<p>/g,
      '</p>\n\n<p>'
    );

    return improvedContent;
  }

  // Fix malformed HTML links
  fixMalformedLinks(content) {
    // First, fix nested <a> tags by removing inner ones
    content = content.replace(
      /<a href="<a href="([^"]*)"[^>]*>([^<]*)<\/a>([^"]*)"[^>]*>([^<]*)<\/a>/g,
      '<a href="$1" target="_blank">$4</a>'
    );

    // Fix broken link patterns - Pattern 1: URL with broken attributes
    content = content.replace(
      /(https:\/\/[^\s"]+)"\s*target="_blank"\s*rel="noopener noreferrer"\s*class="[^"]*">([^<]*)<[^>]*>/g,
      '<a href="$1" target="_blank">$2</a>'
    );

    // Fix broken link patterns - Pattern 1b: Simple URL with target="_blank"
    content = content.replace(
      /(https:\/\/[^\s"]+)"\s*target="_blank"/g,
      '<a href="$1" target="_blank">$1</a>'
    );

    // Fix broken link patterns - Pattern 2: Remove orphaned SVG and attributes
    content = content.replace(
      /rel="noopener noreferrer"\s*class="[^"]*">[^<]*<svg[^>]*>.*?<\/svg>/g,
      ''
    );

    // Fix broken link patterns - Pattern 3: Clean up orphaned attributes
    content = content.replace(
      /"\s*target="_blank"\s*rel="noopener noreferrer"\s*class="[^"]*"/g,
      ''
    );

    return content;
  }

  // Improved fallback content generation
  generateFallbackBlogContent(keyword, title, companyContext, targetWordCount) {
    console.log('🎯 Generating fallback blog content for:', keyword);
    
    const companyName = companyContext.name || 'WattMonk';
    const services = companyContext.servicesOffered || 'Solar Design, Engineering, Permitting, Installation Support';
    
    const sections = Math.ceil(targetWordCount / 400); // ~400 words per section
    let content = '<p>Understanding <b>' + keyword + '</b> is crucial for modern solar energy systems. At <b>' + companyName + '</b>, our expertise in ' + services + ' has helped clients achieve optimal results with ' + keyword + ' implementations.</p>';

    // Generate multiple sections
    for (let i = 1; i <= sections; i++) {
      const sectionTitle = this.generateSectionTitle(keyword, i);
      const sectionContent = this.generateSectionContent(keyword, companyName, services, i);
      
      content += '\n<h2 style=\'color: #FBD46F; font-family: Roboto; font-weight: 600;\'>' + sectionTitle + '</h2>\n' + sectionContent;
    }

    // Add conclusion
    content += '\n<p>In conclusion, <b>' + keyword + '</b> represents a significant opportunity for solar energy optimization. <b>' + companyName + '</b> continues to lead the industry with innovative ' + services + ' solutions. Contact our team to learn more about implementing ' + keyword + ' in your solar projects.</p>';

    return {
      success: true,
      content: {
        title: title,
        content: content,
        internalLinks: [],
        externalLinks: []
      },
      wordCount: this.countWords(content),
      message: 'Fallback content generated successfully'
    };
  }

  // Enhanced block content generation with context awareness
  async generateBlockContent(prompt, blockType, companyContext, blogContext = {}) {
    console.log('🎯 Generating', blockType, 'block with context awareness');

    const keyword = blogContext.keyword || companyContext.keyword || 'solar energy solutions';
    const companyName = companyContext.name || 'WattMonk';
    const services = companyContext.servicesOffered || 'Solar Design, Engineering, Permitting, Installation Support';

    // Check if Vertex AI is available
    if (!this.vertexAI || !this.generativeModel) {
      console.log('⚠️ Vertex AI not available, using enhanced fallback for', blockType, 'block');
      return this.generateFallbackBlockContent(blockType, keyword, companyName, services);
    }

    // Build context-aware prompt based on block type and position
    let contextualPrompt = this.buildBlockContextPrompt(prompt, blockType, keyword, companyName, services, blogContext);

    try {
      const result = await this.generateContent(contextualPrompt, companyContext);
      let content = result.content;

      // Clean and format the content
      content = this.cleanGeneratedContent(content);
      content = this.formatBlockContent(content, blockType);

      return {
        content: content,
        keywords: this.extractKeywords(content),
        wordCount: content.split(' ').length,
        source: result.source || 'vertex-ai'
      };
    } catch (error) {
      console.error('Block generation failed, using fallback:', error.message);
      return this.generateFallbackBlockContent(blockType, keyword, companyName, services);
    }
  }

  buildBlockContextPrompt(prompt, blockType, keyword, companyName, services, blogContext) {
    let contextualPrompt = '';

    // Add strict block-specific instructions
    if (blockType === 'introduction') {
      contextualPrompt += `TASK: Write ONLY an introduction paragraph (2-3 paragraphs maximum) for a blog about "${keyword}"\n\n`;
      contextualPrompt += `REQUIREMENTS:\n`;
      contextualPrompt += `- Write ONLY the introduction - NO other sections\n`;
      contextualPrompt += `- Do NOT include headings, conclusions, or full article content\n`;
      contextualPrompt += `- Length: 100-150 words maximum\n`;
      contextualPrompt += `- Introduce the topic of ${keyword}\n`;
      contextualPrompt += `- Mention ${companyName} naturally (once)\n`;
      contextualPrompt += `- Set up what the article will cover\n`;
      contextualPrompt += `- Use the keyword "${keyword}" naturally\n\n`;
      contextualPrompt += `EXAMPLE FORMAT:\n`;
      contextualPrompt += `<p>Opening sentence about ${keyword}...</p>\n`;
      contextualPrompt += `<p>At ${companyName}, our experience in ${services} has shown...</p>\n`;
      contextualPrompt += `<p>This article will explore...</p>\n\n`;

    } else if (blockType === 'section') {
      contextualPrompt += `TASK: Write ONLY a single section about "${keyword}" - NOT a complete article\n\n`;
      contextualPrompt += `REQUIREMENTS:\n`;
      contextualPrompt += `- Write ONLY one focused section - NO introduction or conclusion\n`;
      contextualPrompt += `- Do NOT include article headings or full blog structure\n`;
      contextualPrompt += `- Length: 150-250 words maximum\n`;
      contextualPrompt += `- Focus on specific aspects of ${keyword}\n`;
      contextualPrompt += `- Include practical information and insights\n`;
      contextualPrompt += `- Mention ${companyName} expertise naturally (once)\n`;
      contextualPrompt += `- Use bullet points or lists if helpful\n\n`;
      contextualPrompt += `EXAMPLE FORMAT:\n`;
      contextualPrompt += `<p>Specific information about ${keyword}...</p>\n`;
      contextualPrompt += `<ul><li>Key point 1</li><li>Key point 2</li></ul>\n`;
      contextualPrompt += `<p>${companyName}'s experience shows...</p>\n\n`;

    } else if (blockType === 'conclusion') {
      contextualPrompt += `TASK: Write ONLY a conclusion paragraph for a blog about "${keyword}"\n\n`;
      contextualPrompt += `REQUIREMENTS:\n`;
      contextualPrompt += `- Write ONLY the conclusion - NO other content\n`;
      contextualPrompt += `- Do NOT repeat the entire article or include new sections\n`;
      contextualPrompt += `- Length: 80-120 words maximum\n`;
      contextualPrompt += `- Summarize key benefits of ${keyword}\n`;
      contextualPrompt += `- Include a clear call-to-action for ${companyName}\n`;
      contextualPrompt += `- End with contact encouragement\n\n`;
      contextualPrompt += `EXAMPLE FORMAT:\n`;
      contextualPrompt += `<p>In summary, ${keyword} offers...</p>\n`;
      contextualPrompt += `<p>Ready to optimize your ${keyword} strategy? Contact ${companyName} today...</p>\n\n`;
    }

    contextualPrompt += `SPECIFIC REQUEST: ${prompt}\n\n`;
    contextualPrompt += `IMPORTANT: Generate ONLY the requested ${blockType} content. Do NOT write a complete article.`;

    return contextualPrompt;
  }

  formatBlockContent(content, blockType) {
    if (!content) return '';

    // Remove any HTML document structure
    content = content.replace(/<!DOCTYPE[^>]*>/gi, '');
    content = content.replace(/<html[^>]*>/gi, '');
    content = content.replace(/<\/html>/gi, '');
    content = content.replace(/<head[^>]*>[\s\S]*?<\/head>/gi, '');
    content = content.replace(/<body[^>]*>/gi, '');
    content = content.replace(/<\/body>/gi, '');
    content = content.replace(/<title[^>]*>[\s\S]*?<\/title>/gi, '');

    // Remove any headings from content blocks (they should be pure content)
    if (blockType === 'introduction' || blockType === 'section' || blockType === 'conclusion') {
      content = content.replace(/<h[1-6][^>]*>.*?<\/h[1-6]>/gi, '');
    }

    // Remove any conclusion-like content from introduction and section blocks
    if (blockType === 'introduction' || blockType === 'section') {
      // Remove phrases that indicate conclusions
      content = content.replace(/In conclusion[^.]*\./gi, '');
      content = content.replace(/To summarize[^.]*\./gi, '');
      content = content.replace(/In summary[^.]*\./gi, '');
      content = content.replace(/Contact us today[^.]*\./gi, '');
      content = content.replace(/Ready to get started[^.]*\./gi, '');
    }

    // Ensure content is properly wrapped in paragraphs
    if (blockType === 'introduction' || blockType === 'section' || blockType === 'conclusion') {
      if (!content.includes('<p>') && content.trim().length > 0) {
        // Split by double line breaks and wrap each part in <p> tags
        const paragraphs = content.split(/\n\s*\n/).filter(p => p.trim());
        content = paragraphs.map(p => `<p>${p.trim()}</p>`).join('\n');
      }
    }

    // Clean up extra whitespace
    content = content.replace(/\s+/g, ' ');
    content = content.replace(/>\s+</g, '><');

    return content.trim();
  }

  generateFallbackBlockContent(blockType, keyword, companyName, services) {
    const titleKeyword = keyword.charAt(0).toUpperCase() + keyword.slice(1);
    const cleanKeyword = keyword.toLowerCase();

    if (blockType === 'introduction') {
      return {
        content: `<p>In today's rapidly evolving solar industry, understanding <b>${cleanKeyword}</b> is crucial for achieving optimal project outcomes. At <b>${companyName}</b>, our extensive experience in ${services} has enabled us to help clients navigate the complexities of ${cleanKeyword} implementation successfully.</p>

<p>Our comprehensive approach combines cutting-edge technology with proven methodologies to deliver exceptional results. Through years of working with diverse solar projects, we've developed deep expertise in ${cleanKeyword} optimization, ensuring that every installation meets the highest industry standards while maximizing performance and efficiency.</p>

<p>Whether you're a solar professional looking to enhance your ${cleanKeyword} knowledge or a property owner considering solar installation, understanding the fundamentals of ${cleanKeyword} is essential for making informed decisions that will benefit your project for decades to come.</p>`,
        keywords: [cleanKeyword, 'solar', 'industry', 'implementation', 'optimization'],
        wordCount: 135
      };
    } else if (blockType === 'section') {
      return {
        content: `<p><b>${titleKeyword}</b> encompasses a wide range of technologies, methodologies, and best practices that are essential for successful solar energy projects. Professional implementation requires a thorough understanding of system design principles, component selection criteria, and optimization strategies.</p>

<p>At <b>${companyName}</b>, our team specializes in ${services}, bringing years of hands-on experience to every project. We've identified several key factors that contribute to successful ${cleanKeyword} implementation:</p>

<ul>
<li><b>Technical Excellence</b> - Utilizing advanced design tools and engineering principles to optimize system performance</li>
<li><b>Quality Assurance</b> - Implementing rigorous testing and validation procedures throughout the project lifecycle</li>
<li><b>Regulatory Compliance</b> - Ensuring all installations meet local codes, standards, and permitting requirements</li>
<li><b>Performance Monitoring</b> - Establishing comprehensive monitoring systems to track and optimize long-term performance</li>
</ul>

<p>Our systematic approach to ${cleanKeyword} ensures that every project delivers maximum value while maintaining the highest standards of safety and reliability. This methodology has proven successful across hundreds of installations, from residential rooftops to large-scale commercial systems.</p>`,
        keywords: [cleanKeyword, 'implementation', 'optimization', 'performance', 'compliance'],
        wordCount: 185
      };
    } else if (blockType === 'conclusion') {
      return {
        content: `<p>Successfully implementing <b>${cleanKeyword}</b> requires a combination of technical expertise, industry knowledge, and attention to detail. Throughout this guide, we've explored the key considerations and best practices that contribute to optimal project outcomes.</p>

<p><b>${companyName}</b> brings decades of combined experience in ${services} to every project we undertake. Our proven track record demonstrates our commitment to delivering exceptional results while maintaining the highest standards of quality and professionalism.</p>

<p>Ready to optimize your ${cleanKeyword} strategy? Our team of experts is here to help you navigate the complexities of solar energy implementation. Contact <b>${companyName}</b> today to schedule a consultation and discover how our comprehensive approach can benefit your next solar project.</p>`,
        keywords: [cleanKeyword, 'implementation', 'expertise', 'consultation', 'optimization'],
        wordCount: 115
      };
    }

    // Default fallback for other block types
    return {
      content: `<p>Professional <b>${cleanKeyword}</b> solutions require expertise and attention to detail. At <b>${companyName}</b>, our comprehensive ${services} ensure optimal results for every solar energy project. Our team combines technical knowledge with practical experience to deliver solutions that meet the highest industry standards.</p>

<p>Whether you're planning a new installation or optimizing an existing system, understanding the principles of ${cleanKeyword} is essential for achieving long-term success. Contact our experts to learn more about how we can help optimize your solar energy strategy.</p>`,
      keywords: [cleanKeyword, 'professional', 'solutions', 'expertise'],
      wordCount: 85
    };
  }

  generateSectionTitle(keyword, sectionNumber) {
    const titles = [
      'Understanding ' + keyword.charAt(0).toUpperCase() + keyword.slice(1) + ' Fundamentals',
      'Benefits and Advantages of ' + keyword,
      'Implementation Best Practices for ' + keyword,
      'Technical Considerations in ' + keyword,
      'Optimizing ' + keyword + ' Performance',
      'Future Trends in ' + keyword
    ];
    
    return titles[(sectionNumber - 1) % titles.length];
  }

  generateSectionContent(keyword, companyName, services, sectionNumber) {
    const contents = [
      '<p><b>' + keyword.charAt(0).toUpperCase() + keyword.slice(1) + '</b> encompasses various technologies and methodologies essential for modern solar energy systems. Professional implementation requires understanding of system design, component selection, and optimization strategies.</p>' +
      '<ul>' +
      '<li><b>System efficiency</b> improvements through proper planning</li>' +
      '<li><b>Cost optimization</b> via strategic component selection</li>' +
      '<li><b>Long-term reliability</b> through quality implementation</li>' +
      '</ul>' +
      '<p>At <b>' + companyName + '</b>, our ' + services + ' ensure that every ' + keyword + ' project meets the highest industry standards.</p>',

      '<p>The advantages of implementing <b>' + keyword + '</b> solutions extend beyond immediate energy savings. Professional installations deliver long-term value through improved system performance and reduced maintenance requirements.</p>' +
      '<table>' +
      '<thead><tr><th>Benefit</th><th>Impact</th><th>Timeline</th></tr></thead>' +
      '<tbody>' +
      '<tr><td><b>Energy Efficiency</b></td><td>15-25% improvement</td><td>Immediate</td></tr>' +
      '<tr><td><b>Cost Savings</b></td><td>Significant reduction</td><td>1-2 years</td></tr>' +
      '<tr><td><b>System Reliability</b></td><td>Enhanced performance</td><td>Long-term</td></tr>' +
      '</tbody>' +
      '</table>' +
      '<p><i>' + companyName + '</i> leverages industry expertise to maximize these benefits for every client.</p>',

      '<p>Successful <b>' + keyword + '</b> implementation requires adherence to industry best practices and proven methodologies. Our systematic approach ensures optimal results for every project.</p>' +
      '<ol>' +
      '<li><b>Initial Assessment</b> - Comprehensive site evaluation</li>' +
      '<li><b>System Design</b> - Custom solutions using advanced modeling</li>' +
      '<li><b>Professional Installation</b> - Certified technician implementation</li>' +
      '<li><b>System Commissioning</b> - Performance verification and testing</li>' +
      '</ol>' +
      '<p>Through our comprehensive ' + services + ', <b>' + companyName + '</b> delivers exceptional ' + keyword + ' solutions.</p>'
    ];
    
    return contents[(sectionNumber - 1) % contents.length];
  }

  // UTILITY METHODS (fixed)
  extractKeywords(text) {
    if (!text) {
      return [];
    }
    
    const words = text.toLowerCase().match(/\b[a-z]{3,}\b/g) || [];
    const frequency = {};
    
    for (let i = 0; i < words.length; i++) {
      const word = words[i];
      frequency[word] = (frequency[word] || 0) + 1;
    }
    
    const entries = Object.entries(frequency);
    entries.sort(function(a, b) {
      return b[1] - a[1];
    });
    
    return entries.slice(0, 10).map(function(entry) {
      return entry[0];
    });
  }

  countWords(text) {
    if (!text || typeof text !== 'string') {
      return 0;
    }
    
    // Remove HTML tags for accurate word count
    const cleanText = text.replace(/<[^>]*>/g, ' ');
    const words = cleanText.trim().split(/\s+/).filter(function(word) {
      return word.length > 0;
    });
    return words.length;
  }

  truncateContent(content, wordLimit) {
    if (!content) {
      return '';
    }
    
    const words = content.split(/\s+/);
    if (words.length <= wordLimit) {
      return content;
    }
    
    const truncated = words.slice(0, wordLimit).join(' ');
    return truncated + '...';
  }

  calculateSEOScore(content, keyword, metaTitle, metaDescription) {
    let score = 0;
    if (!content || !keyword) {
      return 0;
    }

    const keywordLower = keyword.toLowerCase();
    const wordCount = this.countWords(content);

    // Keyword in title (15 points)
    if (metaTitle && metaTitle.toLowerCase().includes(keywordLower)) {
      score += 15;
    }

    // Keyword in meta description (10 points)
    if (metaDescription && metaDescription.toLowerCase().includes(keywordLower)) {
      score += 10;
    }

    // Content length (20 points)
    if (wordCount >= 1200) {
      score += 20;
    } else if (wordCount >= 800) {
      score += 15;
    } else if (wordCount >= 500) {
      score += 10;
    }

    // Keyword density (15 points)
    const keywordRegex = new RegExp(keywordLower, 'gi');
    const keywordMatches = content.match(keywordRegex);
    const keywordCount = keywordMatches ? keywordMatches.length : 0;
    const density = (keywordCount / wordCount) * 100;
    if (density >= 0.5 && density <= 2.5) {
      score += 15;
    } else if (density >= 0.3 && density <= 3.0) {
      score += 10;
    }

    // Additional factors
    if (content.includes('<h2')) {
      score += 10; // Has headings
    }
    if (content.includes('<ul') || content.includes('<ol')) {
      score += 5; // Has lists
    }
    if (metaTitle && metaTitle.length <= 60) {
      score += 10; // Good title length
    }
    if (metaDescription && metaDescription.length >= 140 && metaDescription.length <= 160) {
      score += 10; // Good meta length
    }

    return Math.min(score, 100);
  }

  cleanContext(obj) {
    if (!obj || typeof obj !== 'object') {
      return {};
    }

    const cleaned = {};
    const keys = Object.keys(obj);

    for (let i = 0; i < keys.length; i++) {
      const key = keys[i];
      const value = obj[key];

      if (value === null || value === undefined) {
        cleaned[key] = '';
      } else if (typeof value === 'object' && !Array.isArray(value)) {
        // Skip complex objects that shouldn't be in content
        if (value._id || value.constructor.name === 'ObjectId') {
          cleaned[key] = '';
        } else if (value.name && typeof value.name === 'string') {
          // Extract just the name from objects
          cleaned[key] = value.name;
        } else {
          // For simple objects, extract meaningful string representation
          cleaned[key] = value.toString ? value.toString() : '';
        }
      } else if (Array.isArray(value)) {
        cleaned[key] = value.join(', ');
      } else {
        cleaned[key] = String(value);
      }
    }

    return cleaned;
  }

  // Additional utility methods for meta content and keyword generation
  async generateMetaContent(title, companyContext) {
    const companyName = companyContext && companyContext.name ? companyContext.name : 'WattMonk';
    const prompt = 'Generate SEO-optimized meta title (50-60 characters) and meta description (150-160 characters) for: "' + title + '". Include "' + companyName + '". Format as: Title: [title] | Description: [description]';

    try {
      const result = await this.generateContent(prompt, companyContext);
      const content = result.content;
      
      const titleMatch = content.match(/Title:\s*(.+?)(?:\s*\|\s*Description:|$)/);
      const descMatch = content.match(/Description:\s*(.+)$/);
      
      const metaTitle = titleMatch ? titleMatch[1].trim() : title + ' | ' + companyName;
      const metaDescription = descMatch ? descMatch[1].trim() : 'Learn about ' + title.toLowerCase() + ' with ' + companyName + '. Expert insights and solutions.';

      return {
        metaTitle: metaTitle.length > 60 ? metaTitle.substring(0, 57) + '...' : metaTitle,
        metaDescription: metaDescription.length > 160 ? metaDescription.substring(0, 157) + '...' : metaDescription
      };
    } catch (error) {
      console.error('Meta content generation error:', error);
      return {
        metaTitle: title + ' | ' + companyName,
        metaDescription: 'Learn about ' + title.toLowerCase() + ' with ' + companyName + '. Expert insights and professional solutions.'
      };
    }
  }

  async generateKeywordSuggestions(focusKeyword, companyContext) {
    const prompt = 'Generate 15 related keywords for "' + focusKeyword + '" in solar industry. List them separated by commas.';

    try {
      const result = await this.generateContent(prompt, companyContext);
      const keywords = result.content.split(',').map(function(k) {
        return k.trim();
      }).filter(function(k) {
        return k.length > 0;
      });
      return keywords;
    } catch (error) {
      console.error('Keyword generation error:', error);
      return [];
    }
  }

  /**
   * Image generation method - redirects to appropriate image service
   * Note: Gemini is primarily a text model, so we redirect to dedicated image services
   */
  async generateImages(prompt, options = {}) {
    try {
      console.log('🎨 Image generation requested via Gemini service, redirecting to image service...');

      // Import image service dynamically to avoid circular dependencies
      const imageService = require('./imageService');

      // Extract options with defaults
      const {
        style = 'realistic',
        imageType = 'featured',
        blogTitle = '',
        customTitle = ''
      } = options;

      // Call the dedicated image service
      const result = await imageService.generateImageWithAI(prompt, style, imageType, blogTitle, customTitle);

      return {
        success: true,
        images: [result], // Return as array to match expected format
        message: 'Image generated successfully'
      };

    } catch (error) {
      console.error('Image generation error:', error);

      // Return a structured error response
      return {
        success: false,
        images: [],
        error: error.message || 'Image generation failed',
        message: 'Image generation is not directly supported by Gemini. Please use the dedicated image service.'
      };
    }
  }
}

// Create the main service instance
const geminiServiceInstance = new GeminiService();

// Create an imageModel object that the frontend expects
geminiServiceInstance.imageModel = {
  generateImages: async (prompt, options = {}) => {
    try {
      console.log('🎨 ImageModel.generateImages called with:', prompt);

      // Import image service to avoid circular dependency
      const imageService = require('./imageService');

      // Call the image service directly
      const result = await imageService.generateImageWithAI(
        prompt,
        options.style || 'realistic',
        options.imageType || 'featured',
        options.blogTitle || '',
        options.customTitle || ''
      );

      return {
        success: true,
        images: [result],
        data: result,
        message: 'Image generated successfully'
      };
    } catch (error) {
      console.error('ImageModel.generateImages error:', error);
      return {
        success: false,
        images: [],
        error: error.message,
        message: 'Image generation failed'
      };
    }
  }
};

// Export the service instance with imageModel attached
module.exports = geminiServiceInstance;
