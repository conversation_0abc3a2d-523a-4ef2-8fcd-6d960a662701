#!/bin/bash

# Docker optimization and cleanup script
echo "🧹 Docker Optimization and Cleanup"

# Function to display size
show_size() {
    echo "📊 Current Docker usage:"
    docker system df
    echo ""
}

# Show initial size
show_size

# Clean up unused containers
echo "🗑️  Removing stopped containers..."
docker container prune -f

# Clean up unused images
echo "🗑️  Removing unused images..."
docker image prune -f

# Clean up unused volumes
echo "🗑️  Removing unused volumes..."
docker volume prune -f

# Clean up unused networks
echo "🗑️  Removing unused networks..."
docker network prune -f

# Clean up build cache
echo "🗑️  Removing build cache..."
docker builder prune -f

# Show final size
echo "✅ Cleanup complete!"
show_size

# Optimize images
echo "🔧 Optimizing Docker images..."

# Check if images exist and show their sizes
echo "📦 Current image sizes:"
docker images | grep -E "(blog_gen|ai-blog-platform)" || echo "No project images found"

echo ""
echo "💡 Optimization tips:"
echo "   - Use multi-stage builds (already implemented)"
echo "   - Use Alpine Linux base images (already implemented)"
echo "   - Minimize layers in Dockerfile"
echo "   - Use .dockerignore files (already implemented)"
echo "   - Regular cleanup with this script"

echo ""
echo "🚀 To rebuild optimized images:"
echo "   docker-compose -f docker-compose.prod.yml build --no-cache"
