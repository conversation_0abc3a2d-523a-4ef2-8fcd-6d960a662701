const vertexService = require('./geminiService'); // Renamed for clarity (uses Vertex SDK)

class SEOOptimizationService {
  constructor() {
    this.rankMathCriteria = {
      // Basic SEO Requirements for 85-100/100 score
      focusKeywordInTitle: { weight: 15, required: true },
      focusKeywordInMetaDescription: { weight: 10, required: true },
      focusKeywordInURL: { weight: 10, required: true },
      focusKeywordInFirst10Percent: { weight: 15, required: true },
      focusKeywordInContent: { weight: 10, required: true },
      contentLength: { weight: 10, minWords: 1102, required: true },
      titleReadability: { weight: 10, maxLength: 60, required: true },
      contentReadability: { weight: 10, required: true },
      metaDescriptionLength: { weight: 5, minLength: 140, maxLength: 160 },
      keywordDensity: { weight: 5, minDensity: 0.5, maxDensity: 2.5 },
      internalLinks: { weight: 5, minLinks: 2 },
      externalLinks: { weight: 5, minLinks: 1 }
    };
  }

  /**
   * Generate SEO-optimized content that scores 85-100/100 in RankMath
   * @param {Object} contentData - Content generation parameters
   * @returns {Object} SEO-optimized content structure
   */
  async generateSEOOptimizedContent(contentData) {
    const {
      selectedKeyword,
      selectedH1,
      selectedMetaTitle,
      selectedMetaDescription,
      companyName,
      companyContext,
      targetWordCount = 2500,
      articleFormat = 'guide',
      targetAudience = 'Solar industry professionals',
      objective = 'Education'
    } = contentData;

    console.log(`🎯 GENERATING SEO-OPTIMIZED CONTENT FOR RANKMATH 85-100/100 SCORE`);
    console.log(`   Focus Keyword: "${selectedKeyword}"`);
    console.log(`   Target Word Count: ${targetWordCount}`);
    console.log(`   Article Format: ${articleFormat}`);
    console.log(`   Target Audience: ${targetAudience}`);
    console.log(`   Objective: ${objective}`);

    // Step 1: Optimize meta data for RankMath
    const optimizedMeta = await this.optimizeMetaData({
      keyword: selectedKeyword,
      h1: selectedH1,
      metaTitle: selectedMetaTitle,
      metaDescription: selectedMetaDescription,
      companyName
    });

    // Step 2: Generate keyword-optimized content structure
    const contentStructure = await this.generateKeywordOptimizedStructure(selectedKeyword, targetWordCount);

    // Step 3: Generate real working links for the content
    let realLinks = { inboundLinks: [], outboundLinks: [] };
    try {
      const linkService = require('./linkService');
      realLinks = await linkService.generateInboundOutboundLinks(selectedKeyword, companyName);
      console.log(`🔗 Generated ${realLinks.inboundLinks.length} real internal and ${realLinks.outboundLinks.length} real external links`);
    } catch (error) {
      console.warn('⚠️ LinkService not available, using fallback links');
    }

    // Step 4: Create SEO-compliant content blocks with keyword criteria and real links
    const contentBlocks = await this.generateSEOContentBlocks(
      contentStructure,
      selectedKeyword,
      companyName,
      companyContext,
      { articleFormat, targetAudience, objective },
      realLinks
    );

    // Step 4: Validate SEO compliance
    const seoValidation = this.validateSEOCompliance(contentBlocks, selectedKeyword, optimizedMeta);

    return {
      optimizedMeta,
      contentBlocks,
      seoValidation,
      estimatedRankMathScore: seoValidation.score,
      seoRecommendations: seoValidation.recommendations
    };
  }

  /**
   * Optimize meta data for maximum RankMath score
   */
  async optimizeMetaData(metaData) {
    const { keyword, h1, metaTitle, metaDescription, companyName } = metaData;

    const optimizationPrompt = `Optimize these meta elements for RankMath SEO to achieve 90-100/100 score:

Focus Keyword: "${keyword}"
Current H1: "${h1}"
Current Meta Title: "${metaTitle}"
Current Meta Description: "${metaDescription}"
Company: ${companyName}

RankMath Requirements:
1. H1: Must start with focus keyword, 50-60 characters, compelling, INCLUDE NUMBERS (e.g., "5 Ways", "2024 Guide", "10 Tips")
2. Meta Title: Focus keyword at beginning, include company, 50-60 characters, INCLUDE NUMBERS for better CTR
3. Meta Description: Focus keyword in first 120 characters, CTA, 140-160 characters
4. URL Slug: Focus keyword, hyphens, under 50 characters

Return JSON format:
{
  "optimizedH1": "Focus keyword first, compelling, 50-60 chars",
  "optimizedMetaTitle": "Focus keyword + company + benefit, 50-60 chars",
  "optimizedMetaDescription": "Focus keyword early + benefit + CTA, 140-160 chars",
  "optimizedSlug": "focus-keyword-based-slug",
  "keywordPlacement": "Strategy for keyword placement",
  "estimatedScore": "Expected RankMath score improvement"
}`;

    try {
      const response = await vertexService.generateContent(optimizationPrompt, { name: companyName });
      const optimized = JSON.parse(response.content.replace(/```json|```/g, ''));
      
      // Validate and ensure compliance
      return {
        h1: optimized.optimizedH1 || h1,
        metaTitle: optimized.optimizedMetaTitle || metaTitle,
        metaDescription: optimized.optimizedMetaDescription || metaDescription,
        slug: optimized.optimizedSlug || this.generateSEOSlug(keyword),
        keywordPlacement: optimized.keywordPlacement,
        estimatedScore: optimized.estimatedScore
      };
    } catch (error) {
      console.error('Meta optimization failed:', error);
      return {
        h1: h1,
        metaTitle: metaTitle,
        metaDescription: metaDescription,
        slug: this.generateSEOSlug(keyword),
        keywordPlacement: 'Standard placement',
        estimatedScore: 'Unable to estimate'
      };
    }
  }

  /**
   * Generate keyword-optimized content structure with enhanced RankMath compliance
   */
  async generateKeywordOptimizedStructure(keyword, targetWordCount) {
    // NUCLEAR WORD COUNT CONTROL - FORCE EXACT TARGET
    console.log(`🚨 NUCLEAR WORD COUNT: Target = ${targetWordCount} words EXACTLY`);

    // Calculate section word counts
    const introWords = Math.round(targetWordCount * 0.12);
    const sectionWords = Math.round(targetWordCount * 0.20);
    const conclusionWords = Math.round(targetWordCount * 0.12);
    const totalCalculated = introWords + (sectionWords * 4) + conclusionWords;

    console.log(`📊 Word distribution: Intro(${introWords}) + 4×Sections(${sectionWords}) + Conclusion(${conclusionWords}) = ${totalCalculated} words`);

    const structure = {
      introduction: {
        wordCount: Math.round(targetWordCount * 0.12), // 12% of target (no cap)
        keywordRequirement: 'Must include focus keyword in first 50 words (CRITICAL for RankMath)',
        purpose: 'Hook reader, establish keyword relevance, and preview article value',
        styling: 'Professional paragraph with Roboto font, engaging opener, proper structure'
      },
      mainSections: [
        {
          heading: `What is ${keyword}? Technical Overview`,
          headingStyle: 'H2 with #FBD46F color, Roboto font, Semi Bold (600), proper spacing',
          wordCount: Math.round(targetWordCount * 0.20), // 20% of target (no cap)
          keywordRequirement: 'Include exact keyword phrase 2-3 times naturally in first paragraph',
          purpose: 'Define and explain the main topic with technical details',
          contentStyle: 'Clear paragraphs with technical specifications, professional tone',
          uniqueContent: 'Focus on technical definitions, specifications, and industry standards'
        },
        {
          heading: `Top Benefits of ${keyword} Systems`,
          headingStyle: 'H2 with #FBD46F color, Roboto font, Semi Bold (600), proper spacing',
          wordCount: Math.round(targetWordCount * 0.20), // 20% of target (no cap)
          keywordRequirement: 'Include keyword variations and related terms',
          purpose: 'Highlight specific advantages with real-world examples',
          contentStyle: 'Bullet points with measurable benefits, case studies, data',
          uniqueContent: 'Focus on performance metrics, efficiency gains, environmental impact'
        },
        {
          heading: `${keyword} Installation Process & Requirements`,
          headingStyle: 'H2 with #FBD46F color, Roboto font, Semi Bold (600), proper spacing',
          wordCount: Math.round(targetWordCount * 0.20), // 20% of target (no cap)
          keywordRequirement: 'Include keyword in process descriptions and requirements',
          purpose: 'Explain installation steps, requirements, and timeline',
          contentStyle: 'Step-by-step process with requirements checklist',
          uniqueContent: 'Focus on installation timeline, permits, equipment, site requirements'
        },
        {
          heading: `${keyword} Maintenance & Long-term Performance`,
          headingStyle: 'H2 with #FBD46F color, Roboto font, Semi Bold (600), proper spacing',
          wordCount: Math.round(targetWordCount * 0.20), // 20% of target (no cap)
          keywordRequirement: 'Include keyword with maintenance and performance terms',
          purpose: 'Address maintenance needs, warranty, and long-term performance',
          contentStyle: 'Maintenance schedules, warranty information, performance data',
          uniqueContent: 'Focus on maintenance costs, warranty terms, performance monitoring'
        }
      ],
      conclusion: {
        heading: `${keyword}: Making the Right Choice for Your Project`,
        headingStyle: 'H2 with #FBD46F color, Roboto font, Semi Bold (600), proper spacing',
        wordCount: Math.round(targetWordCount * 0.12), // 12% of target (no cap)
        keywordRequirement: 'Include exact keyword phrase and strong call-to-action language',
        purpose: 'Summarize key points, reinforce benefits, and encourage immediate action',
        contentStyle: 'Strong conclusion with clear CTA, company branding, urgency language'
      }
    };

    return structure;
  }

  /**
   * Generate SEO-compliant content blocks
   */
  async generateSEOContentBlocks(structure, keyword, companyName, companyContext = {}, keywordCriteria = {}, realLinks = { inboundLinks: [], outboundLinks: [] }) {
    const { articleFormat = 'guide', targetAudience = 'Solar industry professionals', objective = 'Education' } = keywordCriteria;
    const blocks = [];
    let blockId = 1;

    // Introduction block with keyword in first 100 words - ENHANCED FOR RANKMATH
    const introPrompt = `Write a compelling, expert-level introduction about "${keyword}" for ${targetAudience}.

ARTICLE CONTEXT:
- Format: ${articleFormat}
- Target Audience: ${targetAudience}
- Objective: ${objective}
- Company: ${companyName}

CRITICAL REQUIREMENTS:
- Write ONLY the introduction content - NO headings or meta-commentary
- ABSOLUTE WORD LIMIT: MAXIMUM ${structure.introduction.wordCount} words - STOP IMMEDIATELY when you reach this limit
- Count every single word as you write and STOP at word ${structure.introduction.wordCount}
- If you exceed ${structure.introduction.wordCount} words, you FAILED the task
- Include "${keyword}" within the first 50 words (CRITICAL for RankMath)
- Write with deep technical expertise and industry knowledge
- Use specific data, statistics, and real-world examples

LINK REQUIREMENTS - ABSOLUTELY CRITICAL - NO EXCEPTIONS:
- FORBIDDEN: Creating ANY fake, placeholder, localhost, or made-up links
- FORBIDDEN: Using [object Object] or any placeholder text
- FORBIDDEN: Creating links that don't exist in the provided list
- MANDATORY: ONLY copy-paste these EXACT working links if you need references:

${this.formatRealLinksOnly(realLinks.outboundLinks.slice(0, 3))}

- If you want to reference something, copy-paste ONE of the above links EXACTLY as shown
- If none of the provided links are suitable, write content WITHOUT any links
- DO NOT modify the URLs or link text in any way
- Avoid generic statements and template language

CONTENT STYLE - EXPERT LEVEL:
- Start with a compelling industry statistic or recent development
- Reference specific technical standards, regulations, or industry benchmarks
- Include actual performance metrics and efficiency numbers
- Mention real challenges that solar professionals face
- Use technical terminology appropriately
- Show deep understanding of the solar industry

EXAMPLE OPENING STYLES:
- "Recent studies show that ${keyword} can improve system performance by 15-25%..."
- "With the latest NEC 2023 requirements, ${keyword} has become critical for..."
- "Industry data reveals that 73% of solar installations underperform due to..."
- "Advanced ${keyword} techniques are now essential for meeting new efficiency standards..."

COMPANY INTEGRATION (NATURAL):
- Company: ${companyName}
- Services: ${companyContext.servicesOffered || 'Solar design and engineering services'}
- Mention ${companyName} once, naturally, showing specific expertise
- Reference actual project experience or industry insights
- Avoid sales language - focus on technical credibility

FORMATTING:
- Use <p> tags for paragraphs
- Use <strong> for key technical terms and metrics
- Include specific numbers, percentages, and data points
- Make content authoritative and credible

Write a technically sophisticated introduction that demonstrates real expertise in ${keyword}.

CRITICAL WORD COUNT RULES:
- MAXIMUM ALLOWED: ${structure.introduction.wordCount} words - NOT ONE WORD MORE
- Write EXACTLY ${Math.floor(structure.introduction.wordCount * 0.9)} to ${structure.introduction.wordCount} words
- Count every single word as you write
- STOP IMMEDIATELY at word ${structure.introduction.wordCount}
- If you write more than ${structure.introduction.wordCount} words, you FAILED
- Quality over quantity - be concise and precise`;

    const introContent = await vertexService.generateContent(introPrompt, companyContext);

    // Validate and adjust word count
    let adjustedContent = this.validateAndAdjustWordCount(
      introContent.content,
      structure.introduction.wordCount,
      'introduction'
    );

    // CRITICAL: Remove any fake links that might have slipped through
    adjustedContent = this.removeFakeLinks(adjustedContent, realLinks);

    // Enhance content with company info and links
    const enhancedIntroContent = vertexService.enhanceContentWithCompanyInfo(
      adjustedContent,
      companyContext,
      keyword
    );

    blocks.push({
      id: `intro-${blockId++}`,
      type: "paragraph",
      content: enhancedIntroContent,
      seoNotes: "Keyword in first 100 words for RankMath compliance"
    });

    // Generate main section blocks
    for (const section of structure.mainSections) {
      // H2 heading
      blocks.push({
        id: `h2-${blockId++}`,
        type: "h2",
        content: section.heading,
        seoNotes: "H2 with focus keyword for content structure"
      });

      // Section content - ENHANCED FOR GENUINE TECHNICAL CONTENT
      const sectionPrompt = `Write expert-level technical content for the "${section.heading}" section about ${keyword}.

ARTICLE CONTEXT:
- Format: ${articleFormat} (adapt writing style accordingly)
- Target Audience: ${targetAudience}
- Objective: ${objective}
- Company: ${companyName}

CRITICAL REQUIREMENTS:
- Write ONLY the section content - NO headings or meta-commentary
- ABSOLUTE WORD LIMIT: MAXIMUM ${section.wordCount} words - STOP IMMEDIATELY when you reach this limit
- Count every single word as you write and STOP at word ${section.wordCount}
- If you exceed ${section.wordCount} words, you FAILED the task
- Include "${keyword}" ${section.keywordRequirement}
- Write with deep technical expertise and industry knowledge
- Use specific data, statistics, and real-world examples

LINK REQUIREMENTS - ABSOLUTELY CRITICAL - NO EXCEPTIONS:
- FORBIDDEN: Creating ANY fake, placeholder, localhost, or made-up links
- FORBIDDEN: Using [object Object] or any placeholder text
- FORBIDDEN: Creating links that don't exist in the provided list
- MANDATORY: ONLY copy-paste these EXACT working links if you need references:

${this.formatRealLinksOnly([...realLinks.outboundLinks.slice(0, 2), ...realLinks.inboundLinks.slice(0, 1)])}

- If you want to reference something, copy-paste ONE of the above links EXACTLY as shown
- If none of the provided links are suitable, write content WITHOUT any links
- DO NOT modify the URLs or link text in any way
- Avoid generic statements and template language

TECHNICAL DEPTH REQUIRED:
- Include specific equipment models, manufacturers, or technologies
- Reference actual efficiency percentages, power ratings, or performance metrics
- Mention relevant codes, standards (UL, IEC, NEC), or regulations
- Discuss real-world installation challenges and solutions
- Use industry-specific terminology and technical concepts
- Include actual case studies or project examples

CONTENT EXAMPLES FOR SOLAR TOPICS:
- "Tier 1 modules like SunPower X-Series achieve 22.8% efficiency..."
- "According to NREL data, proper ${keyword} can reduce LCOE by 12-18%..."
- "IEC 61215 testing standards require modules to withstand 2400 Pa wind loads..."
- "String inverters from Fronius and SMA typically operate at 97-98% efficiency..."
- "NEC 2023 Section 690.12 mandates rapid shutdown within 30 seconds..."

STRUCTURE REQUIREMENTS:
- Start with technical facts or industry data
- Include specific numbers, percentages, and measurements
- Reference actual equipment, standards, or regulations
- Provide actionable technical insights
- Use bullet points for technical specifications
- Include real-world implementation details

FORMATTING:
- Use <p> tags for paragraphs
- Use <strong> for technical terms and key metrics
- Use <ul><li> for technical specifications or benefits
- Use <ol><li> for step-by-step processes
- Include specific numbers and data points

COMPANY INTEGRATION (TECHNICAL FOCUS):
- Company: ${companyName}
- Services: ${companyContext.servicesOffered || 'Solar design and engineering services'}
- Mention ${companyName} once with specific technical expertise
- Reference actual project experience or technical capabilities
- Focus on technical credibility, not sales language

Write technically sophisticated content that demonstrates real expertise in ${keyword}.

CRITICAL WORD COUNT RULES:
- MAXIMUM ALLOWED: ${section.wordCount} words - NOT ONE WORD MORE
- Write EXACTLY ${Math.floor(section.wordCount * 0.9)} to ${section.wordCount} words
- Count every single word as you write
- STOP IMMEDIATELY at word ${section.wordCount}
- If you write more than ${section.wordCount} words, you FAILED
- Quality over quantity - be concise and precise`;

      const sectionContent = await vertexService.generateContent(sectionPrompt, companyContext);

      // Validate and adjust word count
      let adjustedSectionContent = this.validateAndAdjustWordCount(
        sectionContent.content,
        section.wordCount,
        'section'
      );

      // CRITICAL: Remove any fake links that might have slipped through
      adjustedSectionContent = this.removeFakeLinks(adjustedSectionContent, realLinks);

      // Enhance section content with company info and links
      const enhancedSectionContent = vertexService.enhanceContentWithCompanyInfo(
        adjustedSectionContent,
        companyContext,
        keyword
      );

      blocks.push({
        id: `section-${blockId++}`,
        type: "paragraph",
        content: enhancedSectionContent,
        seoNotes: section.keywordRequirement
      });
    }

    // Conclusion block - ENHANCED FOR GENUINE EXPERT CONTENT
    const conclusionPrompt = `Write a professional conclusion for the "${keyword}" article that demonstrates industry expertise.

ARTICLE CONTEXT:
- Format: ${articleFormat}
- Target Audience: ${targetAudience}
- Objective: ${objective}
- Company: ${companyName}

CRITICAL REQUIREMENTS:
- Write ONLY the conclusion content - NO headings or meta-commentary
- ABSOLUTE WORD LIMIT: MAXIMUM ${structure.conclusion.wordCount} words - STOP IMMEDIATELY when you reach this limit
- Count every single word as you write and STOP at word ${structure.conclusion.wordCount}
- If you exceed ${structure.conclusion.wordCount} words, you FAILED the task
- Include "${keyword}" naturally in the summary
- Focus on technical insights and industry implications

LINK REQUIREMENTS - ABSOLUTELY CRITICAL - NO EXCEPTIONS:
- FORBIDDEN: Creating ANY fake, placeholder, localhost, or made-up links
- FORBIDDEN: Using [object Object] or any placeholder text
- FORBIDDEN: Creating links that don't exist in the provided list
- MANDATORY: ONLY copy-paste these EXACT working links if you need references:

${this.formatRealLinksOnly(realLinks.inboundLinks.slice(0, 1))}

- If you want to reference something, copy-paste ONE of the above links EXACTLY as shown
- If none of the provided links are suitable, write content WITHOUT any links
- DO NOT modify the URLs or link text in any way
- Avoid overly promotional language

CONCLUSION STYLE - EXPERT LEVEL:
- Summarize key technical insights and industry implications
- Reference future trends or emerging technologies
- Highlight measurable benefits and performance improvements
- Discuss industry best practices and standards
- Provide actionable technical recommendations

CONTENT STRUCTURE:
- Start with technical summary of key points
- Include specific performance metrics or efficiency gains
- Reference industry standards or regulatory considerations
- Discuss implementation best practices
- End with professional insights about industry direction

TECHNICAL FOCUS:
- Include specific numbers, percentages, or performance metrics
- Reference relevant standards, codes, or regulations
- Mention emerging technologies or industry trends
- Discuss real-world implementation considerations
- Provide actionable technical insights

COMPANY INTEGRATION (PROFESSIONAL):
- Company: ${companyName}
- Services: ${companyContext.servicesOffered || 'Solar design and engineering services'}
- Mention ${companyName} once, naturally, as industry expert
- Reference technical capabilities or project experience
- Focus on expertise and technical credibility
- Avoid aggressive sales language

FORMATTING:
- Use <p> tags for paragraphs
- Use <strong> for key technical terms and metrics
- Include specific data points and industry insights
- Maintain professional, authoritative tone

Write a technically sophisticated conclusion that demonstrates expertise in ${keyword} and positions ${companyName} as an industry authority.

CRITICAL WORD COUNT RULES:
- MAXIMUM ALLOWED: ${structure.conclusion.wordCount} words - NOT ONE WORD MORE
- Write EXACTLY ${Math.floor(structure.conclusion.wordCount * 0.9)} to ${structure.conclusion.wordCount} words
- Count every single word as you write
- STOP IMMEDIATELY at word ${structure.conclusion.wordCount}
- If you write more than ${structure.conclusion.wordCount} words, you FAILED
- Quality over quantity - be concise and precise`;

    const conclusionContent = await vertexService.generateContent(conclusionPrompt, companyContext);

    // Enhance conclusion with company info and strong CTA
    let enhancedConclusionContent = vertexService.enhanceContentWithCompanyInfo(
      conclusionContent.content,
      companyContext,
      keyword
    );

    // CRITICAL: Remove any fake links that might have slipped through
    enhancedConclusionContent = this.removeFakeLinks(enhancedConclusionContent, realLinks);

    // Add strong company-specific CTA if missing
    if (!enhancedConclusionContent.includes('contact') && !enhancedConclusionContent.includes('visit')) {
      enhancedConclusionContent += `\n\nReady to optimize your ${keyword} strategy? Contact ${companyContext.name || 'our team'} today for expert ${companyContext.servicesOffered || 'solar services'}. Visit https://www.wattmonk.com to learn more about our comprehensive solutions.`;
    }

    blocks.push({
      id: `conclusion-${blockId++}`,
      type: "paragraph",
      content: enhancedConclusionContent,
      seoNotes: "Conclusion with keyword and CTA"
    });

    // Add References section with REAL working links
    const referencesContent = this.generateReferencesSection(realLinks, keyword);

    blocks.push({
      id: `references-${blockId++}`,
      type: "references",
      content: referencesContent,
      seoNotes: "Reference links for credibility and authority"
    });

    return blocks;
  }

  /**
   * Format ONLY real working links for AI prompts - prevents fake link generation
   * @param {Array} links - Array of link objects
   * @returns {string} Formatted real links only
   */
  formatRealLinksOnly(links) {
    if (!links || links.length === 0) {
      return `
1. <a href="https://www.energy.gov/eere/solar/" target="_blank" rel="noopener noreferrer" class="font-medium text-sm text-blue-600 hover:text-blue-800 underline flex items-center gap-1">Department of Energy Solar Research<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-external-link h-3 w-3"><path d="M15 3h6v6"></path><path d="M10 14 21 3"></path><path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path></svg></a>
2. <a href="https://www.nrel.gov/solar/" target="_blank" rel="noopener noreferrer" class="font-medium text-sm text-blue-600 hover:text-blue-800 underline flex items-center gap-1">NREL Solar Research<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-external-link h-3 w-3"><path d="M15 3h6v6"></path><path d="M10 14 21 3"></path><path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path></svg></a>
3. <a href="https://www.seia.org/solar-industry-research-data" target="_blank" rel="noopener noreferrer" class="font-medium text-sm text-blue-600 hover:text-blue-800 underline flex items-center gap-1">SEIA Industry Data<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-external-link h-3 w-3"><path d="M15 3h6v6"></path><path d="M10 14 21 3"></path><path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path></svg></a>`;
    }

    return links.map((link, index) => {
      return `${index + 1}. <a href="${link.url}" target="_blank" rel="noopener noreferrer" class="font-medium text-sm text-blue-600 hover:text-blue-800 underline flex items-center gap-1">${link.text}<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-external-link h-3 w-3"><path d="M15 3h6v6"></path><path d="M10 14 21 3"></path><path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path></svg></a>`;
    }).join('\n');
  }

  /**
   * Format links for use in AI prompts with proper styling and external link icons
   * @param {Array} links - Array of link objects
   * @returns {string} Formatted links string
   */
  formatLinksForPrompt(links) {
    if (!links || links.length === 0) {
      return `<a href="https://www.nrel.gov/solar/" target="_blank">NREL Solar Research</a>, <a href="https://www.seia.org/" target="_blank">SEIA Industry Data</a>`;
    }

    return links.map(link => {
      // SIMPLE blue links that open in new tabs
      return `<a href="${link.url}" target="_blank">${link.text}</a>`;
    }).join(', ');
  }

  /**
   * Generate references section with real working links
   * @param {Object} realLinks - Object containing inbound and outbound links
   * @param {string} keyword - Focus keyword
   * @returns {string} HTML references section
   */
  generateReferencesSection(realLinks, keyword) {
    const allLinks = [...realLinks.outboundLinks, ...realLinks.inboundLinks];

    if (allLinks.length === 0) {
      // Fallback to static links if no real links available
      return `<h3 style="color: #FBD46F; font-family: Roboto; font-weight: 600;">References</h3>
<ul>
  <li><a href="https://www.nrel.gov/solar/" target="_blank" rel="noopener">NREL Solar Research</a> - National Renewable Energy Laboratory solar technology research and data</li>
  <li><a href="https://www.seia.org/" target="_blank" rel="noopener">SEIA Industry Data</a> - Solar Energy Industries Association market reports and statistics</li>
  <li><a href="https://www.energy.gov/solar/" target="_blank" rel="noopener">DOE Solar Programs</a> - U.S. Department of Energy solar energy initiatives and resources</li>
</ul>`;
    }

    let referencesHTML = `<h3 style="color: #FBD46F; font-family: Roboto; font-weight: 600;">References</h3>\n<ul>\n`;

    // Use up to 5 real links
    const linksToUse = allLinks.slice(0, 5);

    linksToUse.forEach(link => {
      const linkText = link.text || `${keyword} Resource`;
      const linkUrl = link.url || '#';
      const linkContext = link.context || `Authority content about ${keyword}`;

      referencesHTML += `  <li><a href="${linkUrl}" target="_blank" rel="noopener">${linkText}</a> - ${linkContext}</li>\n`;
    });

    referencesHTML += `</ul>`;

    return referencesHTML;
  }

  /**
   * Validate SEO compliance and calculate RankMath score
   */
  validateSEOCompliance(contentBlocks, keyword, metaData) {
    const validation = {
      score: 0,
      maxScore: 100,
      checks: {},
      recommendations: []
    };

    // Combine all content for analysis
    const allContent = contentBlocks
      .filter(block => block.type === 'paragraph')
      .map(block => block.content)
      .join(' ');

    const wordCount = allContent.split(' ').length;
    const keywordCount = this.countKeywordOccurrences(allContent, keyword);
    const keywordDensity = (keywordCount / wordCount) * 100;

    // Check 1: Focus keyword in title (15 points)
    if (metaData.h1 && metaData.h1.toLowerCase().includes(keyword.toLowerCase())) {
      validation.score += 15;
      validation.checks.keywordInTitle = true;
    } else {
      validation.recommendations.push('Include focus keyword in H1 title');
    }

    // Check 2: Focus keyword in meta description (10 points)
    if (metaData.metaDescription && metaData.metaDescription.toLowerCase().includes(keyword.toLowerCase())) {
      validation.score += 10;
      validation.checks.keywordInMetaDescription = true;
    } else {
      validation.recommendations.push('Include focus keyword in meta description');
    }

    // Check 3: Focus keyword in URL (10 points)
    if (metaData.slug && metaData.slug.includes(keyword.toLowerCase().replace(/\s+/g, '-'))) {
      validation.score += 10;
      validation.checks.keywordInURL = true;
    } else {
      validation.recommendations.push('Include focus keyword in URL slug');
    }

    // Check 4: Focus keyword in first 100 words (15 points) - ENHANCED FOR RANKMATH
    const first100Words = allContent.split(' ').slice(0, 100).join(' ');
    if (first100Words.toLowerCase().includes(keyword.toLowerCase())) {
      validation.score += 15;
      validation.checks.keywordInFirst100Words = true;
    } else {
      validation.recommendations.push('Include focus keyword in first 100 words of content (CRITICAL for RankMath)');
    }

    // Check 5: Focus keyword found in content (10 points)
    if (keywordCount > 0) {
      validation.score += 10;
      validation.checks.keywordInContent = true;
    } else {
      validation.recommendations.push('Include focus keyword in content');
    }

    // Check 6: Content length (10 points)
    if (wordCount >= 1102) {
      validation.score += 10;
      validation.checks.contentLength = true;
    } else {
      validation.recommendations.push(`Increase content length to at least 1102 words (current: ${wordCount})`);
    }

    // Check 7: Title readability (10 points)
    if (metaData.h1 && metaData.h1.length <= 60) {
      validation.score += 10;
      validation.checks.titleReadability = true;
    } else {
      validation.recommendations.push('Keep title under 60 characters');
    }

    // Check 8: Content readability (10 points)
    validation.score += 10; // Assume good readability with our structured approach
    validation.checks.contentReadability = true;

    // Check 9: Meta description length (5 points)
    if (metaData.metaDescription && metaData.metaDescription.length >= 140 && metaData.metaDescription.length <= 160) {
      validation.score += 5;
      validation.checks.metaDescriptionLength = true;
    } else {
      validation.recommendations.push('Meta description should be 140-160 characters');
    }

    // Check 10: Keyword density (5 points)
    if (keywordDensity >= 0.5 && keywordDensity <= 2.5) {
      validation.score += 5;
      validation.checks.keywordDensity = true;
    } else {
      validation.recommendations.push(`Adjust keyword density to 0.5-2.5% (current: ${keywordDensity.toFixed(2)}%)`);
    }

    // Additional RankMath-specific checks

    // Check 11: H2 headings with keyword (5 points)
    const h2Count = (allContent.match(/<h2[^>]*>/gi) || []).length;
    const h2WithKeyword = (allContent.match(new RegExp(`<h2[^>]*>.*${keyword}.*</h2>`, 'gi')) || []).length;
    if (h2WithKeyword >= 1) {
      validation.score += 5;
      validation.checks.keywordInH2 = true;
    } else {
      validation.recommendations.push('Include focus keyword in at least one H2 heading');
    }

    // Check 12: Internal links (5 points)
    const internalLinks = (allContent.match(/<a[^>]*href[^>]*>/gi) || []).length;
    if (internalLinks >= 2) {
      validation.score += 5;
      validation.checks.internalLinks = true;
    } else {
      validation.recommendations.push('Add at least 2 internal links for better SEO');
    }

    // Check 13: Image alt text with keyword (5 points)
    const imageAltWithKeyword = (allContent.match(new RegExp(`alt="[^"]*${keyword}[^"]*"`, 'gi')) || []).length;
    if (imageAltWithKeyword >= 1) {
      validation.score += 5;
      validation.checks.keywordInImageAlt = true;
    } else {
      validation.recommendations.push('Include focus keyword in at least one image alt text');
    }

    validation.keywordDensity = keywordDensity;
    validation.wordCount = wordCount;
    validation.keywordCount = keywordCount;
    validation.h2Count = h2Count;
    validation.h2WithKeyword = h2WithKeyword;

    // Determine overall grade - ENHANCED FOR RANKMATH (Target: 85-88/100)
    if (validation.score >= 85) {
      validation.grade = 'A';
      validation.status = 'Excellent - RankMath Optimized';
      validation.color = 'green';
    } else if (validation.score >= 75) {
      validation.grade = 'B+';
      validation.status = 'Good - Near RankMath Target';
      validation.color = 'orange';
    } else if (validation.score >= 65) {
      validation.grade = 'B';
      validation.status = 'Good - Needs RankMath Optimization';
      validation.color = 'orange';
    } else {
      validation.grade = 'C';
      validation.status = 'Needs Major RankMath Improvements';
      validation.color = 'red';
    }

    return validation;
  }

  /**
   * Count keyword occurrences in content
   */
  countKeywordOccurrences(content, keyword) {
    const regex = new RegExp(keyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'gi');
    const matches = content.match(regex);
    return matches ? matches.length : 0;
  }

  /**
   * Generate SEO-friendly slug
   */
  generateSEOSlug(text) {
    return text
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '')
      .substring(0, 50);
  }

  /**
   * Regenerate individual content block with real links
   */
  async regenerateContentBlock(blockType, keyword, companyName, companyContext = {}, targetWordCount = 400, customPrompt = null, sectionTitle = null) {
    console.log(`🔄 Regenerating ${blockType} block with SEO optimization and real links`);

    // Get real links for the keyword
    const linkService = require('./linkService');
    let realLinks = { inboundLinks: [], outboundLinks: [] };

    try {
      realLinks = await linkService.generateInboundOutboundLinks(keyword, companyName);
      console.log(`🔗 Retrieved ${realLinks.inboundLinks.length} inbound and ${realLinks.outboundLinks.length} outbound links for regeneration`);
    } catch (error) {
      console.warn('⚠️ LinkService not available for regeneration, using fallback links');
    }

    let prompt = '';
    let seoNotes = '';

    if (blockType === 'introduction') {
      prompt = customPrompt || `Write a compelling, expert-level introduction about "${keyword}" for solar industry professionals.

CRITICAL INSTRUCTIONS:
- ABSOLUTE WORD LIMIT: MAXIMUM ${targetWordCount} words - STOP IMMEDIATELY when you reach this limit
- Count every single word as you write and STOP at word ${targetWordCount}
- Include "${keyword}" within the first 50 words (CRITICAL for RankMath)
- Write with deep technical expertise and industry knowledge
- Use specific data, statistics, and real-world examples

LINK REQUIREMENTS - ABSOLUTELY CRITICAL - NO EXCEPTIONS:
- FORBIDDEN: Creating ANY fake, placeholder, localhost, or made-up links
- FORBIDDEN: Using [object Object] or any placeholder text
- FORBIDDEN: Creating links that don't exist in the provided list
- MANDATORY: ONLY copy-paste these EXACT working links if you need references:

${this.formatRealLinksOnly(realLinks.outboundLinks.slice(0, 2))}

- If you want to reference something, copy-paste ONE of the above links EXACTLY as shown
- If none of the provided links are suitable, write content WITHOUT any links
- DO NOT modify the URLs or link text in any way

CONTENT STYLE - EXPERT LEVEL:
- Start with a compelling industry statistic or recent development
- Reference specific technical standards, regulations, or industry benchmarks
- Include actual performance metrics and efficiency numbers
- Mention real challenges that solar professionals face
- Use technical terminology appropriately
- Show deep understanding of the solar industry

Company: ${companyName}
Services: ${companyContext.servicesOffered || 'Solar Design, Engineering, Permitting, Installation Support'}

Write ONLY the introduction content - no headings, no other sections.`;
      seoNotes = "Keyword in first 50 words for RankMath compliance";

    } else if (blockType === 'section') {
      // CRITICAL: Generate content that specifically addresses the section title
      const actualSectionTitle = sectionTitle || `${keyword} Professional Guide`;
      const sectionFocus = this.getSectionFocus(actualSectionTitle, keyword);

      prompt = customPrompt || `Write a detailed section specifically about "${actualSectionTitle}" related to "${keyword}" for solar industry professionals.

CRITICAL SECTION FOCUS:
- This section MUST specifically address: "${actualSectionTitle}"
- Content must be directly relevant to the section heading
- DO NOT repeat generic information about "${keyword}"
- Focus on the specific aspect mentioned in the section title

SPECIFIC CONTENT REQUIREMENTS FOR "${actualSectionTitle}":
${sectionFocus}

CRITICAL INSTRUCTIONS:
- ABSOLUTE WORD LIMIT: MAXIMUM ${targetWordCount} words - STOP IMMEDIATELY when you reach this limit
- Count every single word as you write and STOP at word ${targetWordCount}
- Include "${keyword}" naturally 2-3 times throughout the content
- Write with deep technical expertise and industry knowledge
- Use specific data, statistics, and real-world examples

CONTENT REQUIREMENTS:
- NO LINKS of any kind - focus purely on valuable content
- NO HTML tags, NO markdown, NO special formatting
- Write in clean, readable paragraphs
- Make content engaging and valuable for professionals

CONTENT QUALITY REQUIREMENTS:
- Write substantial, detailed content with minimum 3-4 paragraphs
- Each paragraph should be 100-150 words minimum
- Include specific data, statistics, real examples, and case studies
- Use professional but engaging language that demonstrates expertise
- Provide actionable insights and practical advice professionals can implement
- Reference real industry standards, codes, and best practices
- Include specific equipment models, efficiency ratings, and technical specifications
- Add real-world project examples with actual numbers and results

Company: ${companyName}
Services: ${companyContext.servicesOffered || 'Solar Design, Engineering, Permitting, Installation Support'}

Write ONLY the section content - no headings. Focus specifically on "${actualSectionTitle}".`;
      seoNotes = `Technical content specifically about ${actualSectionTitle} with keyword integration`;

    } else if (blockType === 'conclusion') {
      prompt = customPrompt || `Write a professional conclusion for the "${keyword}" article that demonstrates industry expertise.

CRITICAL INSTRUCTIONS:
- ABSOLUTE WORD LIMIT: MAXIMUM ${targetWordCount} words - STOP IMMEDIATELY when you reach this limit
- Count every single word as you write and STOP at word ${targetWordCount}
- Include "${keyword}" naturally in the summary
- Focus on technical insights and industry implications

LINK REQUIREMENTS - ABSOLUTELY CRITICAL - NO EXCEPTIONS:
- FORBIDDEN: Creating ANY fake, placeholder, localhost, or made-up links
- FORBIDDEN: Using [object Object] or any placeholder text
- FORBIDDEN: Creating links that don't exist in the provided list
- MANDATORY: ONLY copy-paste these EXACT working links if you need references:

${this.formatRealLinksOnly(realLinks.inboundLinks.slice(0, 1))}

- If you want to reference something, copy-paste ONE of the above links EXACTLY as shown
- If none of the provided links are suitable, write content WITHOUT any links
- DO NOT modify the URLs or link text in any way

CONCLUSION STYLE - EXPERT LEVEL:
- Summarize key technical insights and industry implications
- Reference future trends or emerging technologies
- Highlight measurable benefits and performance improvements
- Discuss industry best practices and standards
- Provide actionable technical recommendations

Company: ${companyName}
Services: ${companyContext.servicesOffered || 'Solar Design, Engineering, Permitting, Installation Support'}

Write ONLY the conclusion content - no headings.`;
      seoNotes = "Conclusion with keyword and technical insights";

    } else {
      // Generic content block
      prompt = customPrompt || `Write expert content about "${keyword}" for solar industry professionals.

CRITICAL INSTRUCTIONS:
- ABSOLUTE WORD LIMIT: MAXIMUM ${targetWordCount} words - STOP IMMEDIATELY when you reach this limit
- Count every single word as you write and STOP at word ${targetWordCount}
- Include "${keyword}" naturally in the content
- Write with deep technical expertise and industry knowledge

LINK REQUIREMENTS - ABSOLUTELY CRITICAL - NO EXCEPTIONS:
- FORBIDDEN: Creating ANY fake, placeholder, localhost, or made-up links
- FORBIDDEN: Using [object Object] or any placeholder text
- FORBIDDEN: Creating links that don't exist in the provided list
- MANDATORY: ONLY copy-paste these EXACT working links if you need references:

${this.formatRealLinksOnly(realLinks.outboundLinks.slice(0, 2))}

- If you want to reference something, copy-paste ONE of the above links EXACTLY as shown
- If none of the provided links are suitable, write content WITHOUT any links
- DO NOT modify the URLs or link text in any way

Company: ${companyName}
Services: ${companyContext.servicesOffered || 'Solar Design, Engineering, Permitting, Installation Support'}`;
      seoNotes = "Expert content with keyword integration";
    }

    // Generate content using Vertex AI
    const vertexService = require('./geminiService');
    const result = await vertexService.generateContent(prompt, companyContext);

    let content = result.content;

    // Validate and adjust word count
    content = this.validateAndAdjustWordCount(content, targetWordCount, blockType);

    // CRITICAL: Remove any fake links that might have slipped through
    content = this.removeFakeLinks(content, realLinks);

    // ENSURE OUTBOUND LINKS: Add external links to ALL content blocks for better SEO
    if (realLinks.outboundLinks && realLinks.outboundLinks.length > 0) {
      // Add 1-2 contextual outbound links within the content
      const relevantLinks = realLinks.outboundLinks.slice(0, 2);
      relevantLinks.forEach((link, index) => {
        if (index === 0 && link.url && link.text) {
          // Add first link in the middle of content with COMPLETE link tags
          const sentences = content.split('. ');
          if (sentences.length > 3) {
            const midPoint = Math.floor(sentences.length / 2);
            // Create SHORT link text (max 3-4 words)
            const shortLinkText = this.createShortLinkText(link.text, keyword);
            // SIMPLE blue link that opens in new tab
            if (link.url.startsWith('http')) {
              sentences[midPoint] += ` For more information, see <a href="${link.url}" target="_blank">${shortLinkText}</a>.`;
              content = sentences.join('. ');
            }
          }
        }
      });

      // Add simple links at the end if this is conclusion
      if (blockType === 'conclusion' && realLinks.outboundLinks && realLinks.outboundLinks.length > 0) {
        content += this.addOutboundLinksSection(realLinks.outboundLinks, keyword);
      }
    }

    // Clean up content formatting for WordPress
    content = content.replace(/\n{3,}/g, '\n\n'); // Remove excessive line breaks
    content = content.replace(/\s{2,}/g, ' '); // Remove excessive spaces

    // Remove image placeholders that appear as text
    content = content.replace(/\[Image:[^\]]+\]/g, '');

    // Ensure proper paragraph breaks for readability
    content = content.replace(/\. ([A-Z])/g, '.\n\n$1');

    content = content.trim();

    return {
      content: content,
      wordCount: content.split(' ').filter(w => w.trim().length > 0).length,
      seoNotes: seoNotes,
      seoOptimized: true,
      source: 'seo-optimization-service'
    };
  }

  /**
   * Get specific content focus based on section title
   */
  getSectionFocus(sectionTitle, keyword) {
    const title = sectionTitle.toLowerCase();

    if (title.includes('what is') || title.includes('technical overview')) {
      return `- Define what ${keyword} actually means and how it works
- Explain the technical components and processes involved
- Describe the science/technology behind it
- Include technical specifications and parameters
- Explain how it differs from alternatives`;
    }

    if (title.includes('benefits') || title.includes('advantages')) {
      return `- List specific measurable benefits (percentages, cost savings, efficiency gains)
- Explain WHY these benefits occur (technical reasons)
- Compare benefits vs alternatives
- Include real-world performance data
- Discuss long-term advantages`;
    }

    if (title.includes('installation') || title.includes('process') || title.includes('requirements')) {
      return `- Step-by-step installation process
- Required tools, equipment, and materials
- Technical requirements and specifications
- Safety considerations and codes compliance
- Common installation challenges and solutions`;
    }

    if (title.includes('maintenance') || title.includes('performance') || title.includes('long-term')) {
      return `- Specific maintenance procedures and schedules
- Performance monitoring and optimization
- Common issues and troubleshooting
- Long-term performance expectations
- Maintenance costs and requirements`;
    }

    if (title.includes('cost') || title.includes('pricing') || title.includes('roi')) {
      return `- Detailed cost breakdown and pricing factors
- ROI calculations and payback periods
- Cost comparison with alternatives
- Financing options and incentives
- Total cost of ownership analysis`;
    }

    if (title.includes('types') || title.includes('options') || title.includes('comparison')) {
      return `- Different types/options available
- Technical specifications for each type
- Pros and cons comparison
- Best use cases for each option
- Selection criteria and recommendations`;
    }

    // Default for any other section
    return `- Focus specifically on the aspect mentioned in "${sectionTitle}"
- Provide detailed, technical information about this specific topic
- Include practical examples and real-world applications
- Avoid repeating general information about ${keyword}`;
  }

  /**
   * Remove fake links and replace with real ones or remove entirely
   */
  removeFakeLinks(content, realLinks) {
    if (!content) return content;

    // Remove any localhost links
    content = content.replace(/href="[^"]*localhost[^"]*"/g, '');

    // Remove any [object Object] references
    content = content.replace(/\[object\s+Object\]/g, '');
    content = content.replace(/\{\s*name:\s*[^}]*\}/g, ''); // Remove object literals

    // ENHANCED: Remove database object references that leak into content
    content = content.replace(/\{\s*name:\s*'[^']*',\s*description:\s*'[^']*',\s*_id:\s*new ObjectId\([^)]*\)\s*\}/g, '');
    content = content.replace(/new ObjectId\([^)]*\)/g, '');
    content = content.replace(/\{\s*\n\s*name:\s*'[^']*',[\s\S]*?\}/g, ''); // Multi-line object literals

    // Remove any incomplete or broken link tags
    content = content.replace(/<a[^>]*href="[^"]*\[object[^"]*"[^>]*>.*?<\/a>/g, '');
    content = content.replace(/<a[^>]*href=""[^>]*>.*?<\/a>/g, '');

    // CRITICAL: Remove incomplete <a href=" tags without closing
    content = content.replace(/<a href="[^"]*"?\s*$/g, '');
    content = content.replace(/<a href="\s*$/g, '');
    content = content.replace(/<a href="$/g, '');
    content = content.replace(/href="\s*$/g, '');

    // Remove malformed links with embedded SVG or class attributes in URLs
    content = content.replace(/href="[^"]*class="[^"]*"/g, 'href="#"');
    content = content.replace(/href="[^"]*svg[^"]*"/g, 'href="#"');
    content = content.replace(/href="[^"]*lucide[^"]*"/g, 'href="#"');

    // Remove broken link fragments that appear as text
    content = content.replace(/https?:\/\/[^"\s]*" target="_blank" rel="noopener noreferrer"[^.]*\./g, '');
    content = content.replace(/target="_blank" rel="noopener noreferrer"[^<]*</g, '<');

    // Remove any links that contain debug, undefined, or placeholder text
    content = content.replace(/<a[^>]*href="[^"]*debug[^"]*"[^>]*>.*?<\/a>/g, '');
    content = content.replace(/<a[^>]*href="[^"]*undefined[^"]*"[^>]*>.*?<\/a>/g, '');
    content = content.replace(/<a[^>]*href="[^"]*placeholder[^"]*"[^>]*>.*?<\/a>/g, '');

    // Clean up broken link structures with embedded HTML
    content = content.replace(/href="[^"]*" target="_blank" rel="noopener noreferrer" class="[^"]*">/g, '" target="_blank" rel="noopener noreferrer">');

    // Remove SVG code that got embedded in content
    content = content.replace(/<svg[^>]*>.*?<\/svg>/g, '');
    content = content.replace(/http:\/\/www\.w3\.org\/2000\/svg[^"]*"/g, '"');

    // ENHANCED: Remove broken SVG elements and malformed links
    content = content.replace(/<a href="w3\.org\/2000\/svg"[^>]*>.*?<\/a>/g, '');
    content = content.replace(/w3\.org\/2000\/svg[^>]*>/g, '');
    content = content.replace(/stroke-width="[^"]*"\s*stroke-linecap="[^"]*"[^>]*>/g, '');
    content = content.replace(/class="lucide lucide-external-link[^"]*"[^>]*>/g, '');
    content = content.replace(/viewBox="[^"]*"\s*fill="[^"]*"[^>]*>/g, '');

    // FINAL CLEANUP: Remove any remaining broken elements
    content = content.replace(/<a href="<path[^>]*>/g, ''); // Remove broken path links
    content = content.replace(/<path[^>]*>/g, ''); // Remove orphaned path elements
    content = content.replace(/\{\s*\n\s*name:\s*'[^']*',[\s\S]*?\n\}/g, ''); // Multi-line objects at end
    content = content.replace(/Contact WattMonk today for expert \{[\s\S]*?\}/g, 'Contact WattMonk today for expert solar services'); // Fix broken CTA

    // AGGRESSIVE: Remove any remaining database object patterns
    content = content.replace(/\{\s*name:\s*'[^']*',[\s\S]*?\}/g, ''); // Any remaining object patterns
    content = content.replace(/new ObjectId\([^)]*\)/g, ''); // Any remaining ObjectId references
    content = content.replace(/\n\s*\}\s*\.\s*Visit/g, '. Visit'); // Fix broken endings

    // Clean up any orphaned link text that might be left
    content = content.replace(/\(\s*\)/g, ''); // Remove empty parentheses
    content = content.replace(/\s+/g, ' '); // Clean up extra spaces
    content = content.trim();

    console.log('🧹 Cleaned fake links and malformed HTML from content');
    return content;
  }

  /**
   * Create short, clean link text (max 3-4 words)
   */
  createShortLinkText(originalText, keyword) {
    if (!originalText) return 'Learn More';

    // Remove common prefixes and suffixes
    let shortText = originalText
      .replace(/^(Read more about|Learn more about|More information on|Details about|Guide to|Information on)/i, '')
      .replace(/(here|now|today|guide|information|details)$/i, '')
      .trim();

    // If still too long, extract key words
    const words = shortText.split(' ');
    if (words.length > 4) {
      // Try to keep keyword-related words
      const keywordWords = words.filter(word =>
        word.toLowerCase().includes(keyword.toLowerCase().split(' ')[0]) ||
        ['solar', 'energy', 'panel', 'installation', 'system', 'guide', 'tips'].includes(word.toLowerCase())
      );

      if (keywordWords.length > 0 && keywordWords.length <= 4) {
        shortText = keywordWords.slice(0, 3).join(' ');
      } else {
        // Take first 3 meaningful words
        shortText = words.slice(0, 3).join(' ');
      }
    }

    // Fallback to generic text if still too long or empty
    if (shortText.length > 30 || shortText.length < 3) {
      return 'Learn More';
    }

    return shortText;
  }

  /**
   * Add outbound links section for SEO
   */
  addOutboundLinksSection(outboundLinks, keyword) {
    if (!outboundLinks || outboundLinks.length === 0) return '';

    // Simple links section (like the original project)
    let linksSection = '\n\n';

    outboundLinks.slice(0, 3).forEach(link => {
      if (link && link.url && link.text) {
        // Create SHORT link text and SIMPLE blue link
        const shortText = this.createShortLinkText(link.text, keyword);
        linksSection += `<a href="${link.url}" target="_blank">${shortText}</a>\n`;
      }
    });

    console.log(`🔗 Added simple links section with ${outboundLinks.length} links`);
    return linksSection;
  }

  /**
   * Validate and adjust word count to match target
   */
  validateAndAdjustWordCount(content, targetWordCount, blockType) {
    if (!content || !targetWordCount) {
      return content;
    }

    // Remove HTML tags for word counting
    const textContent = content.replace(/<[^>]*>/g, ' ').replace(/\s+/g, ' ').trim();
    const words = textContent.split(' ').filter(word => word.length > 0);
    const currentWordCount = words.length;

    console.log(`📊 ${blockType} word count: ${currentWordCount}/${targetWordCount} words`);

    // FORCE EXACT WORD COUNT - NO TOLERANCE
    if (currentWordCount > targetWordCount) {
      console.log(`🚨 FORCE TRIMMING ${blockType}: ${currentWordCount} → ${targetWordCount} words`);
      // Immediately trim to exact count
    } else {
      console.log(`✅ ${blockType} word count acceptable: ${currentWordCount}/${targetWordCount}`);
      return content;
    }

    // NUCLEAR OPTION: Cut to EXACT word count
    console.log(`✂️ NUCLEAR TRIM ${blockType}: ${currentWordCount} → EXACTLY ${targetWordCount} words`);

    // Take EXACTLY the target number of words - NO EXCEPTIONS
    const exactWords = words.slice(0, targetWordCount);
    let exactText = exactWords.join(' ');

    // Add period if needed (but don't count it as a word)
    if (!exactText.endsWith('.') && !exactText.endsWith('!') && !exactText.endsWith('?')) {
      exactText = exactText + '.';
    }

    // Final verification
    const finalWordCount = exactText.split(' ').filter(w => w.trim().length > 0).length;
    console.log(`🎯 EXACT TRIM RESULT: ${finalWordCount}/${targetWordCount} words`);

    // Return with HTML structure
    return `<p>${exactText}</p>`;
  }
}

module.exports = new SEOOptimizationService();
