/**
 * Check environment variables
 */

// Load environment variables FIRST
require('dotenv').config();

console.log('🔍 Checking Environment Variables...\n');

const requiredEnvVars = [
  'SERP_API_KEY',
  'SERPER_API_KEY', 
  'PERPLEXITY_API_KEY',
  'RAPIDAPI_KEY',
  'GOOGLE_APPLICATION_CREDENTIALS',
  'VERTEX_AI_PROJECT_ID',
  'VERTEX_AI_LOCATION'
];

console.log('📋 Required Environment Variables:');
requiredEnvVars.forEach(varName => {
  const value = process.env[varName];
  const status = value ? '✅' : '❌';
  const displayValue = value ? (value.length > 20 ? value.substring(0, 20) + '...' : value) : 'NOT SET';
  console.log(`   ${status} ${varName}: ${displayValue}`);
});

console.log('\n🔧 SERP API Status:');
console.log(`   SERP_API_KEY: ${process.env.SERP_API_KEY ? 'SET' : 'NOT SET'}`);
console.log(`   SERPER_API_KEY: ${process.env.SERPER_API_KEY ? 'SET' : 'NOT SET'}`);
console.log(`   PERPLEXITY_API_KEY: ${process.env.PERPLEXITY_API_KEY ? 'SET' : 'NOT SET'}`);
console.log(`   RAPIDAPI_KEY: ${process.env.RAPIDAPI_KEY ? 'SET' : 'NOT SET'}`);

if (!process.env.SERP_API_KEY && !process.env.SERPER_API_KEY && !process.env.PERPLEXITY_API_KEY) {
  console.log('\n🚨 CRITICAL: No SERP APIs configured!');
  console.log('   This is why the system is falling back to static links.');
  console.log('   The AI is generating fake links because it\'s not getting real ones.');
}
